#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多数据类型路径管理模块

提供统一的数据文件路径管理功能，支持：
1. 股票数据路径管理（raw/adjusted结构）
2. 期货数据路径管理（raw/continuous结构）
3. 自动数据类型识别和路径分发
4. 分区存储路径生成和管理

架构设计：
- PathManagerFactory: 路径管理器工厂，根据数据类型分发
- StockPathManager: 股票路径管理器
- FuturesPathManager: 期货路径管理器
- BasePathManager: 基础路径管理器抽象类
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Literal
from datetime import datetime
from pathlib import Path

from config.settings import DATA_ROOT
from utils.logger.manager import get_unified_logger
from utils.smart_time_converter import smart_to_datetime

logger = get_unified_logger(__name__)



class DataType:
    """数据类型常量"""
    STOCK = "stock"
    FUTURES = "futures"


class AdjustmentType:
    """复权类型常量"""
    NONE = "none"
    FRONT = "front"
    BACK = "back"


class ContinuousType:
    """连续化类型常量"""
    NONE = "none"
    MAIN = "main"
    WEIGHTED = "weighted"


def detect_data_type(symbol: str) -> str:
    """检测数据类型

    Args:
        symbol: 证券代码，如000001.SZ、rb00.SF

    Returns:
        数据类型：stock或futures
    """
    if '.' not in symbol:
        raise ValueError(f"无效的证券代码格式: {symbol}")

    _, exchange = symbol.split('.')
    exchange = exchange.upper()

    # 股票交易所
    if exchange in ['SZ', 'SH']:
        return DataType.STOCK
    # 期货交易所
    elif exchange in ['SF', 'IF', 'ZF', 'DF']:
        return DataType.FUTURES
    else:
        raise ValueError(f"不支持的交易所: {exchange}")


def parse_symbol(symbol: str) -> Tuple[str, str]:
    """解析证券代码

    Args:
        symbol: 证券代码，如000001.SZ、rb00.SF

    Returns:
        (代码, 交易所) 元组
    """
    if not isinstance(symbol, str) or '.' not in symbol:
        raise ValueError(f"无效的证券代码格式: {symbol}")

    parts = symbol.split('.')
    if len(parts) != 2 or not parts[0] or not parts[1]:
        raise ValueError(f"无效的证券代码格式: {symbol}")

    code = parts[0]
    exchange = parts[1].upper()

    return code, exchange


class BasePathManager(ABC):
    """基础路径管理器抽象类"""

    def __init__(self, data_root: Optional[str] = None):
        """初始化路径管理器

        Args:
            data_root: 数据根目录
        """
        self.data_root = Path(data_root or DATA_ROOT)
        logger.debug(f"路径管理器初始化，数据根目录: {self.data_root}")

    @abstractmethod
    def get_raw_path(self, symbol: str, period: str, timestamp: Optional[str] = None) -> Path:
        """获取原始数据路径"""
        pass

    @abstractmethod
    def get_processed_path(self, symbol: str, period: str, process_type: str, timestamp: Optional[str] = None) -> Path:
        """获取处理后数据路径"""
        pass

    @abstractmethod
    def get_base_dir(self, symbol: str, period: str, process_type: str = "raw") -> Path:
        """获取基础目录"""
        pass

    def _parse_timestamp(self, timestamp: Optional[str]) -> datetime:
        """解析时间戳

        Args:
            timestamp: 时间戳字符串

        Returns:
            datetime对象
        """
        if timestamp:
            try:
                if isinstance(timestamp, (int, float)):
                    return smart_to_datetime(timestamp)
                elif isinstance(timestamp, str) and len(timestamp) == 8:  # YYYYMMDD
                    return datetime.strptime(timestamp, '%Y%m%d')
                elif isinstance(timestamp, str) and len(timestamp) == 14:  # YYYYMMDDHHMMSS
                    return datetime.strptime(timestamp, '%Y%m%d%H%M%S')
                else:
                    raise ValueError(f"不支持的时间戳格式: {timestamp}")
            except Exception as e:
                logger.warning(f"解析时间戳失败: {e}，使用当前时间")
                return datetime.now()
        else:
            return datetime.now()

    def _get_partition_path(self, base_path: Path, period: str, dt: datetime) -> Path:
        """获取分区路径

        Args:
            base_path: 基础路径
            period: 数据周期
            dt: 时间对象

        Returns:
            分区路径
        """
        if period.lower() == 'tick':
            # tick数据按日分区: base_path/2024/01/01.parquet
            return base_path / f"{dt.year}" / f"{dt.month:02d}" / f"{dt.day:02d}.parquet"
        else:
            # 其他周期按年分区: base_path/2024.parquet
            return base_path / f"{dt.year}.parquet"


class StockPathManager(BasePathManager):
    """股票路径管理器"""

    def get_raw_path(self, symbol: str, period: str, timestamp: Optional[str] = None) -> Path:
        """获取股票原始数据路径

        Args:
            symbol: 股票代码
            period: 数据周期
            timestamp: 时间戳

        Returns:
            原始数据路径
        """
        code, exchange = parse_symbol(symbol)
        dt = self._parse_timestamp(timestamp)

        base_path = self.data_root / "raw" / exchange / code / period
        return self._get_partition_path(base_path, period, dt)

    def get_processed_path(self, symbol: str, period: str, process_type: str, timestamp: Optional[str] = None) -> Path:
        """获取股票复权数据路径

        Args:
            symbol: 股票代码
            period: 数据周期
            process_type: 复权类型（front/back）
            timestamp: 时间戳

        Returns:
            复权数据路径
        """
        if process_type not in [AdjustmentType.FRONT, AdjustmentType.BACK]:
            raise ValueError(f"不支持的复权类型: {process_type}")

        code, exchange = parse_symbol(symbol)
        dt = self._parse_timestamp(timestamp)

        base_path = self.data_root / "adjusted" / exchange / code / process_type / period
        return self._get_partition_path(base_path, period, dt)

    def get_base_dir(self, symbol: str, period: str, process_type: str = "raw") -> Path:
        """获取股票基础目录

        Args:
            symbol: 股票代码
            period: 数据周期
            process_type: 处理类型（raw/front/back）

        Returns:
            基础目录路径
        """
        code, exchange = parse_symbol(symbol)

        if process_type == "raw":
            return self.data_root / "raw" / exchange / code / period
        else:
            return self.data_root / "adjusted" / exchange / code / process_type / period

    def get_dividend_factors_path(self, symbol: str) -> Path:
        """获取复权因子文件路径

        Args:
            symbol: 股票代码

        Returns:
            复权因子文件路径
        """
        code, exchange = parse_symbol(symbol)
        return self.data_root / "raw" / exchange / code / "dividend_factors.parquet"


class FuturesPathManager(BasePathManager):
    """期货路径管理器"""

    def get_raw_path(self, symbol: str, period: str, timestamp: Optional[str] = None) -> Path:
        """获取期货原始数据路径

        Args:
            symbol: 期货代码
            period: 数据周期
            timestamp: 时间戳

        Returns:
            原始数据路径
        """
        code, exchange = parse_symbol(symbol)
        dt = self._parse_timestamp(timestamp)

        base_path = self.data_root / "raw" / exchange / code / period
        return self._get_partition_path(base_path, period, dt)

    def get_processed_path(self, symbol: str, period: str, process_type: str, timestamp: Optional[str] = None) -> Path:
        """获取期货连续化数据路径

        Args:
            symbol: 期货代码
            period: 数据周期
            process_type: 连续化类型（main/weighted）
            timestamp: 时间戳

        Returns:
            连续化数据路径
        """
        if process_type not in [ContinuousType.MAIN, ContinuousType.WEIGHTED]:
            raise ValueError(f"不支持的连续化类型: {process_type}")

        code, exchange = parse_symbol(symbol)
        dt = self._parse_timestamp(timestamp)

        base_path = self.data_root / "continuous" / exchange / code / process_type / period
        return self._get_partition_path(base_path, period, dt)

    def get_base_dir(self, symbol: str, period: str, process_type: str = "raw") -> Path:
        """获取期货基础目录

        Args:
            symbol: 期货代码
            period: 数据周期
            process_type: 处理类型（raw/main/weighted）

        Returns:
            基础目录路径
        """
        code, exchange = parse_symbol(symbol)

        if process_type == "raw":
            return self.data_root / "raw" / exchange / code / period
        else:
            return self.data_root / "continuous" / exchange / code / process_type / period

    def get_continuous_factors_path(self, symbol: str) -> Path:
        """获取连续化因子文件路径

        Args:
            symbol: 期货代码

        Returns:
            连续化因子文件路径
        """
        code, exchange = parse_symbol(symbol)
        return self.data_root / "raw" / exchange / code / "continuous_factors.parquet"


def get_partition_files(data_root: str, market: str, code: str, period: str,
                      start_time: Optional[str] = None,
                      end_time: Optional[str] = None,
                      limit: Optional[int] = None) -> List[str]:
    """获取指定时间范围内的分区文件列表（兼容性函数）

    Args:
        data_root: 数据根目录
        market: 市场代码
        code: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        limit: 限制返回的文件数量

    Returns:
        分区文件路径列表
    """
    # 兼容性实现，委托给PathManagerFactory
    try:
        symbol = f"{code}.{market}"
        path_manager = PathManagerFactory.get_path_manager(symbol)
        base_dir = path_manager.get_base_dir(symbol, period)

        if not base_dir.exists():
            logger.debug(f"基础目录不存在: {base_dir}")
            return []

        # 简化实现，返回所有文件
        result_files = []
        if period.lower() == 'tick':
            for year_dir in sorted(base_dir.iterdir()):
                if year_dir.is_dir() and year_dir.name.isdigit():
                    for month_dir in sorted(year_dir.iterdir()):
                        if month_dir.is_dir() and month_dir.name.isdigit():
                            for day_file in sorted(month_dir.glob("*.parquet")):
                                result_files.append(str(day_file))
        else:
            for year_file in sorted(base_dir.glob("*.parquet")):
                result_files.append(str(year_file))

        return result_files
    except Exception as e:
        logger.error(f"获取分区文件列表失败: {e}")
        return []

class PathManagerFactory:
    """路径管理器工厂类"""

    _stock_manager = None
    _futures_manager = None

    @classmethod
    def get_path_manager(cls, symbol: str, data_root: Optional[str] = None) -> BasePathManager:
        """获取路径管理器

        Args:
            symbol: 证券代码
            data_root: 数据根目录

        Returns:
            对应的路径管理器实例
        """
        data_type = detect_data_type(symbol)

        if data_type == DataType.STOCK:
            if cls._stock_manager is None:
                cls._stock_manager = StockPathManager(data_root)
            return cls._stock_manager
        elif data_type == DataType.FUTURES:
            if cls._futures_manager is None:
                cls._futures_manager = FuturesPathManager(data_root)
            return cls._futures_manager
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

    @classmethod
    def get_stock_manager(cls, data_root: Optional[str] = None) -> StockPathManager:
        """获取股票路径管理器"""
        if cls._stock_manager is None:
            cls._stock_manager = StockPathManager(data_root)
        return cls._stock_manager

    @classmethod
    def get_futures_manager(cls, data_root: Optional[str] = None) -> FuturesPathManager:
        """获取期货路径管理器"""
        if cls._futures_manager is None:
            cls._futures_manager = FuturesPathManager(data_root)
        return cls._futures_manager


# 统一接口函数
def get_data_path(symbol: str, period: str, process_type: str = "raw", timestamp: Optional[str] = None) -> str:
    """获取数据路径（统一接口）

    Args:
        symbol: 证券代码
        period: 数据周期
        process_type: 处理类型（raw/front/back/main/weighted）
        timestamp: 时间戳

    Returns:
        数据路径
    """
    path_manager = PathManagerFactory.get_path_manager(symbol)

    if process_type == "raw":
        return str(path_manager.get_raw_path(symbol, period, timestamp))
    else:
        return str(path_manager.get_processed_path(symbol, period, process_type, timestamp))


def get_base_directory(symbol: str, period: str, process_type: str = "raw") -> str:
    """获取基础目录（统一接口）

    Args:
        symbol: 证券代码
        period: 数据周期
        process_type: 处理类型

    Returns:
        基础目录路径
    """
    path_manager = PathManagerFactory.get_path_manager(symbol)
    return str(path_manager.get_base_dir(symbol, period, process_type))

# 兼容性函数
def get_data_root() -> str:
    """获取数据根目录（兼容性函数）"""
    from config.settings import DATA_ROOT
    return DATA_ROOT


def get_partitioned_path(data_root: str, symbol: str, period: str, timestamp: Optional[str] = None) -> str:
    """获取分区存储路径（兼容性函数）"""
    return get_data_path(symbol, period, "raw", timestamp)


def get_base_dir(data_root: str, symbol: str, period: str) -> str:
    """获取基础目录（兼容性函数）"""
    return get_base_directory(symbol, period, "raw")


def get_latest_partition_file(data_root: str, symbol: str, period: str) -> Optional[str]:
    """获取最新分区文件（兼容性函数）"""
    try:
        path_manager = PathManagerFactory.get_path_manager(symbol)
        base_dir = path_manager.get_base_dir(symbol, period)

        if not base_dir.exists():
            return None

        if period.lower() == 'tick':
            # 查找最新的年/月/日文件
            years = sorted([d for d in base_dir.iterdir() if d.is_dir() and d.name.isdigit()],
                          key=lambda x: int(x.name), reverse=True)
            if not years:
                return None

            months = sorted([d for d in years[0].iterdir() if d.is_dir() and d.name.isdigit()],
                           key=lambda x: int(x.name), reverse=True)
            if not months:
                return None

            day_files = sorted([f for f in months[0].glob("*.parquet")],
                              key=lambda x: int(x.stem), reverse=True)
            if not day_files:
                return None

            return str(day_files[0])
        else:
            # 查找最新的年份文件
            year_files = sorted([f for f in base_dir.glob("*.parquet")],
                               key=lambda x: int(x.stem), reverse=True)
            if not year_files:
                return None

            return str(year_files[0])
    except Exception as e:
        logger.error(f"获取最新分区文件失败: {e}")
        return None


def get_legacy_path(data_root: str, symbol: str, period: str) -> str:
    """获取旧版存储路径（兼容性函数）"""
    code, exchange = parse_symbol(symbol)
    return os.path.join(data_root, exchange, code, f"{period}.parquet")


def get_save_path(data_root: str, symbol: str, period: str, timestamp: Optional[str] = None) -> str:
    """获取数据保存路径（兼容性函数）"""
    return get_data_path(symbol, period, "raw", timestamp)

# 示例用法
if __name__ == '__main__':
    # 测试股票路径管理
    stock_symbol = "000001.SZ"
    print(f"股票数据类型: {detect_data_type(stock_symbol)}")

    stock_manager = PathManagerFactory.get_stock_manager()
    print(f"股票原始数据路径: {stock_manager.get_raw_path(stock_symbol, '1d')}")
    print(f"股票前复权数据路径: {stock_manager.get_processed_path(stock_symbol, '1d', 'front')}")
    print(f"股票复权因子路径: {stock_manager.get_dividend_factors_path(stock_symbol)}")

    # 测试期货路径管理
    futures_symbol = "rb00.SF"
    print(f"期货数据类型: {detect_data_type(futures_symbol)}")

    futures_manager = PathManagerFactory.get_futures_manager()
    print(f"期货原始数据路径: {futures_manager.get_raw_path(futures_symbol, '1d')}")
    print(f"期货主力连续数据路径: {futures_manager.get_processed_path(futures_symbol, '1d', 'main')}")
    print(f"期货连续化因子路径: {futures_manager.get_continuous_factors_path(futures_symbol)}")

    # 测试统一接口
    print(f"统一接口-股票原始数据: {get_data_path(stock_symbol, '1d', 'raw')}")
    print(f"统一接口-期货连续数据: {get_data_path(futures_symbol, '1d', 'main')}")