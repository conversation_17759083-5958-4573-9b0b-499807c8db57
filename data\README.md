# Data模块目录结构说明

经过重构后，data模块现在具有清晰的目录结构和职责分离：

## 📁 目录结构

```
data/
├── 📄 data_main.py          # 🎯 主入口文件 - 系统初始化和模块协调
├── 📄 data_commands.py      # 🔄 兼容接口 - 向后兼容的API转发
├── 📄 批量合成历史数据.py    # 🚀 批量合成脚本 - 高效的数据合成工具
├── 📁 core/                 # 💼 核心业务逻辑
│   ├── operations.py        # 🛠️  数据操作API (下载/读取/列表/汇总/合成)
│   ├── result_manager.py    # 📊 下载结果管理 (保存/读取/合并下载结果)
│   ├── synthesis_result_manager.py # 📊 合成结果管理 (即时保存/进度恢复)
│   └── data_source_manager.py # 🔗 数据源管理
├── 📁 ui/                   # 🖥️  用户界面
│   ├── cli.py              # ⌨️  命令行接口处理
│   └── interactive.py      # 🎮 交互式界面处理
├── 📁 source/              # 📥 数据源提供者
├── 📁 storage/             # 💾 数据存储
└── 📁 handlers/            # 🔧 专门处理器
```

## 🎯 文件职责

### 主要文件 (按重要性排序)

1. **`data_main.py`** - 🎯 **程序主入口**
   - 系统初始化和日志配置  
   - 命令行参数解析
   - 模块协调和调度

2. **`core/operations.py`** - 💼 **核心数据操作**
   - 下载数据 (`download_data`)
   - 读取数据 (`read_data`) 
   - 列出文件 (`list_data`)
   - 数据汇总 (`summarize_data`)
   - 周期合成 (`synthesize_data`)

3. **`ui/interactive.py`** - 🎮 **交互式界面**
   - 菜单显示和用户交互
   - 参数收集和确认
   - 用户体验逻辑
   - ⚠️ **重要变更**: 周期合成功能已移除，请使用批量脚本

4. **`ui/cli.py`** - ⌨️ **命令行接口**
   - 命令行参数解析
   - 命令路由和执行
   - 结果处理

5. **`data_commands.py`** - 🔄 **兼容接口**
   - 向后兼容的API
   - 转发调用到新架构
   - 保持原有接口不变

### 支持文件

6. **`core/result_manager.py`** - 📊 **结果管理**
   - 下载结果的保存和读取
   - 结果合并和状态管理

7. **`core/data_source_manager.py`** - 🔗 **数据源管理**
   - 数据源连接和管理
   - 底层数据获取

## 🔧 使用方式

### 直接运行 (推荐)
```bash
python data/data_main.py --interactive    # 交互模式（不包含合成功能）
python data/data_main.py --download        # 命令行下载模式（从download_results.txt读取股票列表）
python data/批量合成历史数据.py            # 批量数据合成（推荐）
```

### ⚠️ 重要变更说明
**数据下载功能已更新：**
- 系统会自动从 `download_results.txt` 文件中读取未下载的股票列表
- 命令行中的 `--stocks` 参数已废弃
- 交互模式中不再需要手动输入股票代码

**数据合成功能已重构：**
- 交互式合成功能已移除，遵循"一个功能一个实现"原则
- 统一使用 `批量合成历史数据.py` 脚本进行数据合成
- 批量脚本提供更高效、更灵活的合成方案
- **新增即时保存机制**: 支持任务中断时不丢失已处理结果
- **进度恢复功能**: 任务中断后可从上次停止的位置继续处理
- 详细使用说明请参阅 [批量合成使用指南](../docs/批量合成使用指南.md)

### 兼容方式
```bash
python data/data_commands.py download  # 保持原有用法（不再需要--stocks参数）
```

### 编程调用
```python
from data.core.operations import download_data, read_data
from data.ui.interactive import interactive_mode

# 直接调用核心API（stocks参数已废弃，系统从文件读取）
result = download_data(stocks=[])  # stocks参数已废弃

# 启动交互模式
interactive_mode()
```

## ✅ 重构成果

| 指标 | 重构前 | 重构后 | 改进 |
|------|--------|--------|------|
| **总行数** | 2538行 (2个文件) | 1592行 (5个文件) | -37.3% |
| **最大文件** | 1288行 | 652行 | -49.4% |
| **职责分离** | ❌ 混乱 | ✅ 清晰 | 完全分离 |
| **代码重复** | ❌ 严重 | ✅ 消除 | 提取到工具模块 |
| **功能完整性** | ✅ 完整 | ✅ 完整 | 100% 保持 |

## 📋 设计原则

1. **单一职责** - 每个模块只负责一种功能
2. **分层架构** - 业务层、界面层、协调层清晰分离  
3. **向后兼容** - 保持所有原有API不变
4. **可扩展性** - 模块化设计便于维护和扩展
5. **清晰结构** - 目录结构直观，主要文件容易识别

---

# 数据模块详细说明

数据模块是量化交易系统的基础组件，负责提供数据获取、处理和存储功能。

## 核心组件

### 数据源管理器 (`data_source_manager.py`)

提供统一的数据源管理接口，支持多数据源的配置、切换和错误处理：

- 统一数据下载接口
- 数据源切换和故障转移
- 本地数据访问管理
- 数据保存与转换
- 增量数据更新

#### 数据格式化功能

数据源管理器提供了灵活的数据格式化功能，用于查看和处理数据：

- **pd_format函数**：用于格式化DataFrame，支持多种显示模式
  - 支持显示头部数据（data="head"）
  - 支持显示尾部数据（data="tail"）
  - 支持同时显示头部和尾部数据（data="both"）
  - 支持显示全部数据（data="all"）
  - 可自定义显示的行数
  - 返回格式化后的DataFrame，便于后续处理

使用示例：
```python
from data.data_source_manager import pd_format

# 只显示头部10行数据
head_df = pd_format(df, data="head", rows=10)

# 只显示尾部10行数据
tail_df = pd_format(df, data="tail", rows=10)

# 同时显示头部5行和尾部5行数据
both_df = pd_format(df, data="both", head_rows=5, tail_rows=5)

# 显示全部数据
all_df = pd_format(df, data="all")
```

### 数据获取器 (`fetcher/`)

负责从各种数据源获取原始数据：

- **迅投数据获取器** (`xtquant_data.py`)：通过迅投API获取各类市场数据，**保留原始数据格式和结构**，不进行任何标准化处理
- 其他数据源获取器（可扩展）

### 数据存储 (`storage/`)

负责数据的本地存储和读取：

- Parquet格式存储
- 数据路径管理
- 数据元数据管理

**更新说明**：`parquet_storage.py`已更新，移除了自动时间格式转换功能，现在将完全保留原始数据格式，不再将Timestamp对象转换为字符串。这确保了存储的数据与原始数据保持完全一致，便于后续处理和分析。详细信息请参阅 [Parquet存储模块更新说明](../docs/parquet_storage_update.md)。

### 数据命令 (`data_commands.py`)

提供命令行接口，方便用户获取和管理数据：

- 数据下载
- 数据查询
- 数据管理
- 周期合成

#### 会话标记功能

数据命令模块使用标准化的会话标记功能，方便追踪命令执行过程和调试问题：

- 会话开始和结束标记明确标识命令执行的边界
- 每个主要操作都有唯一的会话ID，便于日志检索和问题排查
- 支持嵌套会话，适用于复杂的数据处理流程

使用示例：
```python
from utils.logger import get_unified_logger

# 创建日志记录器
logger = get_unified_logger("my_module")

# 使用任务ID记录会话开始
logger.info("===== 开始执行任务：我的任务 =====")

# 执行任务...
logger.info("正在执行任务...")

# 记录会话结束
logger.info("===== 任务执行完成：我的任务 =====")
```

### 周期处理 (`utils/data_processor/`)

提供数据周期转换和处理功能：

- 支持自定义周期合成（如3分钟、6分钟等非标准周期）
- **数据格式保持**：所有合成的数据都保持与原始数据相同的格式
  - 时间列`time`为毫秒时间戳格式（如：1716255000000）
  - 索引为`YYYYMMDDHHMMSS`格式的字符串（如：20240521093000）

#### 周期转换详解

周期转换功能由`utils/data_processor/period_converter.py`模块提供，主要解决两个问题：

1. **支持自定义周期**：包括3分钟、2小时等迅投API不原生支持的周期
2. **保持数据格式一致性**：确保合成数据与原始数据格式完全一致

关键函数：
- `convert_kline_period(df, target_period)`: 将K线数据转换为目标周期
- `resample_1m_kline(df_1m, target_minutes)`: 将1分钟K线数据重采样为指定分钟数

格式一致性保证：
- 输入：可以接受时间列为毫秒时间戳，或索引为DatetimeIndex的DataFrame
- 输出：
  - 时间列'time'为毫秒时间戳格式（如1716255000000）
  - 时间列'time'位于DataFrame的首位
  - 索引为YYYYMMDDHHMMSS格式的字符串（如20240521093000）
  - 索引名称与原始数据保持一致

使用示例：
```python
# 导入转换函数
from utils.data_processor.period_converter import convert_kline_period

# 将1分钟数据转换为3分钟数据
df_3m = convert_kline_period(df_1m, '3m')

# 将1分钟数据转换为2小时数据
df_2h = convert_kline_period(df_1m, '2h')
```

## 命令行工具使用指南

数据模块提供了两种命令行工具：基本命令行工具 (`data_commands.py`) 和交互式工具 (`data_main.py`)。

### 基本命令行工具 (`data_commands.py`)

提供简单直接的命令行接口，适合脚本和自动化任务使用。

#### 下载数据

```bash
python data_commands.py download --stocks "000001.SZ,600000.SH" --period "1d" --start-date "20230101" --end-date "20231231"
```

参数说明：
- `--stocks, -s`: 股票代码，多个用逗号分隔（必需）
- `--period, -p`: 数据周期，默认为 "1d"
- `--start-date`: 开始日期，格式为 YYYYMMDD
- `--end-date`: 结束日期，格式为 YYYYMMDD
- `--no-incremental`: 关闭增量更新（默认开启增量更新）
- `--dividend, -d`: 除权类型，可选值为 "front"（前复权）、"back"（后复权）、"none"（不复权），默认为 "front"
- `--dir`: 数据保存目录
- `--no-show`: 不显示数据

#### 读取数据

```bash
python data_commands.py read --symbols "000001.SZ" --period "1d" --start-date "20230101" --end-date "20231231"
```

参数说明：
- `--symbols, -s`: 股票代码，多个用逗号分隔（必需）
- `--period, -p`: 数据周期，默认为 "1d"
- `--start-date`: 开始日期，格式为 YYYYMMDD
- `--end-date`: 结束日期，格式为 YYYYMMDD
- `--columns, -c`: 需要的列，多个用逗号分隔
- `--dir`: 数据目录
- `--no-show`: 不显示数据

#### 列出数据文件

```bash
python data_commands.py list --market "SZ" --stock "000001" --period "1d"
```

参数说明：
- `--dir`: 数据目录
- `--market, -m`: 市场代码
- `--stock, -s`: 股票代码
- `--period, -p`: 数据周期

#### 汇总数据

```bash
python data_commands.py summary
```

参数说明：
- `--dir`: 数据目录

#### 合成自定义周期数据

```bash
python data_commands.py convert --symbols "000001.SZ" --source "1m" --target "3m" --start-date "20230101" --end-date "20231231"
```

参数说明：
- `--symbols, -s`: 股票代码，多个用逗号分隔（必需）
- `--source`: 源数据周期，默认为 "1m"
- `--target, -t`: 目标周期，如 "3m"、"2h" 等（必需）
- `--start-date`: 开始日期，格式为 YYYYMMDD
- `--end-date`: 结束日期，格式为 YYYYMMDD
- `--dir`: 数据目录
- `--no-show`: 不显示数据
- `--rows`: 显示的行数，默认为 5

### 交互式工具 (`data_main.py`)

提供更友好的交互式界面和更完整的命令行选项，适合用户直接交互。

#### 启动交互式模式

```bash
python data_main.py
# 或
python data_main.py --interactive
```

#### 自定义显示行数

在下载数据或查看本地数据时，系统提供了简单直接的数据显示选项：

1. 系统会直接询问需要显示的行数：
   - 请输入要显示的头部行数（默认为5行）
   - 请输入要显示的尾部行数（默认为5行）

2. 根据输入的行数，系统会自动确定显示模式：
   - 如果头部和尾部行数都大于0，则同时显示头部和尾部数据
   - 如果只有头部行数大于0，则只显示头部数据
   - 如果只有尾部行数大于0，则只显示尾部数据
   - 输入0表示不显示该部分数据

3. 这样可以根据需要查看更多或更少的数据行，特别适用于大数据集

示例交互流程：
```
请输入要显示的头部行数 (最小: 0), 默认: 5: 10
请输入要显示的尾部行数 (最小: 0), 默认: 5: 15
```

#### 数据显示增强

系统现在会在所有情况下显示数据内容，包括：

1. 下载新数据时，显示数据的头部和尾部内容
2. 增量更新时，显示合并后的数据内容
3. 数据内容相同无需更新时，也会显示本地数据内容

这样可以确保用户在任何情况下都能看到数据的具体情况，方便验证数据的正确性和完整性。数据显示内容会记录在日志文件中，可以随时查看。

#### 命令行选项

```bash
# 下载数据
python data_main.py --download --stocks "000001.SZ" "600000.SH" --period "1d" --start "20230101" --end "20231231" --incremental

# 查看本地数据
python data_main.py --view-data --stocks "000001.SZ" --period "1d" --lines 10

# 列出可用数据文件
python data_main.py --list-files --market "SZ" --code "000001"

# 查看数据汇总
python data_main.py --summarize

# 显示数据存储信息
python data_main.py --storage-info

# 测试日志系统
python data_main.py --test-logging
```

## 数据流程

1. 通过数据获取器从数据源获取原始数据
2. 数据源管理器对获取的数据进行管理和必要的处理
3. 通过数据存储组件将处理后的数据保存到本地
4. 用户可通过数据命令或直接调用API获取和使用数据

## 使用示例

```python
# 导入数据源管理器
from data.data_source_manager import download_history_data

# 下载数据
result = download_history_data(
    stock_list=["000001.SZ", "600000.SH"],
    period="1d",
    start_time="20200101",
    end_time="20201231"
)

# 获取下载的数据
data = result.get("data", {})
```

## 数据注意事项

- 迅投数据源现已更新为返回**原始数据**，不进行任何标准化处理
- 如需标准化处理，请使用 `utils.data_processor` 模块中的相关函数
- 所有数据存储默认使用Parquet格式，支持高效的读写和存储
- 数据存储时**不再自动转换时间格式**，完全保留原始数据格式 
- 所有合成的周期数据都保持**与原始数据相同的格式**，确保了数据处理的一致性

## 日志系统优化

数据模块中的日志系统已进行了优化，主要改进包括：

### 日志记录器统一

- 在模块级别创建一次日志记录器，并在整个模块中复用
- 统一使用`__name__`作为记录器名称，确保日志记录的模块名与实际代码位置一致
- 移除了方法内部的重复日志记录器创建代码
- 统一使用增强模式(enhanced=True)，提供更丰富的日志功能

### 代码优化

- 减少了代码冗余，移除了重复的日志记录器创建代码
- 提高了代码一致性，统一使用模块级别的日志记录器
- 增强了代码可读性，使代码结构更清晰
- 提高了代码可维护性，减少了未来可能的错误和不一致

### 使用示例

```python
from utils.logger import get_unified_logger, LogTarget

# 在模块顶部创建一次日志记录器
logger = get_unified_logger(__name__, enhanced=True)

def my_function():
    # 直接使用模块级别的日志记录器，不再创建新的记录器
    logger.debug("函数开始执行")
    
    # 使用增强模式的功能
    logger.info(LogTarget.FILE, "这条日志只记录到文件")
    
    # 记录执行结果
    logger.debug("函数执行完成")
```

这种优化确保了日志记录的一致性和准确性，同时简化了代码结构，提高了可维护性。

## 下载结果保存机制

下载结果保存机制通过`save_download_results_if_needed`方法统一处理，支持在以下情况实时保存结果：

1. 成功下载股票数据后
2. 异常情况下
3. 识别出股票无需下载时（本地数据已覆盖请求时间范围或数据源无更新）
4. 自定义周期处理中的各种情况

这样确保了即使在程序意外中断的情况下，已经处理的结果也不会丢失，用户可以及时看到哪些股票无需下载。