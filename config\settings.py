#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全局设置模块，提供系统级别的配置参数
"""

import logging
import os
import sys

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 添加项目根目录到Python路径
sys.path.insert(0, PROJECT_ROOT)

# ===== 数据存储配置 =====

# 数据根目录，可通过环境变量覆盖
DATA_ROOT = os.environ.get("QUANT_DATA_ROOT", "D:\\data")

# 数据路径格式化模板，按交易所/股票代码/周期.parquet存储
# 例如: D:\data\SZ\000001\1d.parquet
DATA_PATH_TEMPLATE = os.path.join(
    DATA_ROOT, "{exchange}", "{symbol}", "{period}.parquet"
)

# 支持的周期
SUPPORTED_PERIODS = ["tick", "1m", "5m", "15m", "30m", "1h", "1d", "1w", "1mon"]

# 时区设置，所有时间数据统一使用上海时区
DEFAULT_TIMEZONE = "Asia/Shanghai"

# 是否启用并行存储
ENABLE_PARALLEL_STORAGE = True

# 存储使用的最大线程/进程数(性能提升效果最明显)
STORAGE_MAX_WORKERS = 32

# 存储是否使用线程而不是进程
# 线程适合I/O密集型任务（如文件写入），共享内存，开销小
# 进程适合CPU密集型任务，独立内存空间，不受GIL限制
STORAGE_USE_THREADING = False

# ===== 全局进程池配置 =====

# 是否启用全局进程池
ENABLE_GLOBAL_PROCESS_POOL = True

# 全局进程池最大工作进程数
# None表示使用CPU核心数，建议设置为CPU核心数或更小以避免资源争用
GLOBAL_POOL_MAX_WORKERS = 8

# 全局进程池任务分块大小
# 较大的值可以减少任务调度开销，但可能导致负载不均衡
GLOBAL_POOL_CHUNKSIZE = 10

# ===== 日志配置 =====

# 日志级别
LOG_LEVEL = logging.INFO

# 日志目录 - 统一使用项目根目录下的logs文件夹
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")

# 日志格式
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 默认时间格式
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Debug日志配置
DEBUG_LOG_ENABLED = True  # 是否启用debug日志文件
DEBUG_LOG_LEVEL = "debug"  # debug日志文件的日志级别
DEBUG_LOG_MAX_SIZE = 10 * 1024 * 1024  # debug日志文件大小限制（10MB）
DEBUG_LOG_BACKUP_COUNT = 5  # debug日志文件备份数量
DEBUG_LOG_DAYS_TO_KEEP = 30  # debug日志文件保留天数
DEBUG_LOG_FORMAT = None  # debug日志文件的格式，None表示使用详细格式


# ===== 交易配置 =====

# 交易默认股票池
DEFAULT_SYMBOLS = []

# 默认交易时段 (9:30-11:30, 13:00-15:00)
TRADING_PERIODS = [
    {"start": "09:30:00", "end": "11:30:00"},
    {"start": "13:00:00", "end": "15:00:00"}
]

# 最大持仓股票数量
MAX_POSITIONS = 10

# 单笔下单金额上限
MAX_ORDER_VALUE = 1000000

# 风险管理最大回撤限制 (百分比)
MAX_DRAWDOWN_LIMIT = 20

# ===== 回测配置 =====

# 默认初始资金
DEFAULT_INITIAL_CAPITAL = 1000000

# 默认手续费率
DEFAULT_COMMISSION_RATE = 0.0003

# 默认滑点设置
DEFAULT_SLIPPAGE = 0

# 回测相关配置字典，统一存放回测所需的所有参数
BACKTEST_CONFIG = {
    "commission_rate": DEFAULT_COMMISSION_RATE,
    "slippage": DEFAULT_SLIPPAGE,
    "max_positions": MAX_POSITIONS,
    "allow_t0": False,  # 是否允许T+0交易
    "enable_dynamic_commission": False,  # 是否启用动态手续费
    "enable_risk_control": True,  # 是否启用风险控制
    "max_drawdown": MAX_DRAWDOWN_LIMIT,  # 最大回撤限制
    "benchmark": "000300.SH",  # 默认基准指数
}

# 回测结果保存目录
BACKTEST_RESULT_DIR = os.path.join(PROJECT_ROOT, "backtest_results")

# ===== 数据源配置 =====

# 默认数据源
DEFAULT_DATA_SOURCE = "xtquant"

# 迅投数据源配置
XTQUANT_CONFIG = {
    "api_version": "1.0",
    "timeout": 10,  # 秒
    "retry_times": 3,
    "retry_interval": 1,  # 秒
}

# ===== 系统配置 =====

# 是否启用多进程
ENABLE_MULTIPROCESSING = True

# 最大工作进程数，None表示使用所有可用CPU核心
MAX_WORKERS = None

# ===== 数据处理配置 =====

# 数据验证级别: "strict", "normal", "loose"
DATA_VALIDATION_LEVEL = "normal"

# 错误处理策略: "stop", "continue", "retry"
ERROR_HANDLING_STRATEGY = "continue"

# 是否启用自动类型修复
AUTO_FIX_TYPES = True

# 是否启用严格类型检查
STRICT_TYPE_CHECKING = True

# 是否启用性能监控
ENABLE_PERFORMANCE_MONITORING = True

# 最大内存使用量(MB)
MAX_MEMORY_USAGE_MB = 1024

# ===== 验证配置 =====

# 是否启用时间验证
ENABLE_TIME_VALIDATION = True

# 时间容差(毫秒)
TIME_TOLERANCE_MS = 1

# 是否启用类型验证
ENABLE_TYPE_VALIDATION = True

# 是否启用范围验证
ENABLE_RANGE_VALIDATION = True

# 验证样本大小
VALIDATION_SAMPLE_SIZE = 1000

# ===== 调试配置 =====

# 调试级别: "basic", "detailed", "verbose"
DEBUG_LEVEL = "basic"

# 是否启用数据流跟踪
ENABLE_DATA_FLOW_TRACKING = True

# 是否启用性能分析
ENABLE_PERFORMANCE_PROFILING = True

# 最大调试历史记录数
MAX_DEBUG_HISTORY = 100

# 调试输出文件(None表示不输出到文件)
DEBUG_OUTPUT_FILE = None

# ===== 时间转换器配置 =====

# 缓存大小
TIME_CONVERTER_CACHE_SIZE = 128

# 是否启用监控
TIME_CONVERTER_ENABLE_MONITORING = True

# 是否启用警告
TIME_CONVERTER_ENABLE_WARNINGS = True

# 批量处理阈值
TIME_CONVERTER_BATCH_THRESHOLD = 100

# 缓存最大年龄(秒)
TIME_CONVERTER_MAX_CACHE_AGE = 3600

# ===== 智能缓存配置 =====

# 基础缓存目录
CACHE_DIR = os.path.join(DATA_ROOT, "cache")

# 股票复权缓存配置
ADJUSTED_CACHE_CONFIG = {
    "cache_dir": os.path.join(CACHE_DIR, "adjusted"),
    "unused_days_threshold": 7,  # 7天未使用删除策略
    "enable_cache": True,
    "max_cache_size_mb": 1000,  # 最大缓存大小1GB
    "cleanup_interval_hours": 24,  # 24小时清理一次
    "access_tracker_file": "access_tracker.json"
}

# 期货连续化缓存配置
CONTINUOUS_CACHE_CONFIG = {
    "cache_dir": os.path.join(CACHE_DIR, "continuous"),
    "unused_days_threshold": 7,  # 7天未使用删除策略
    "enable_cache": True,
    "max_cache_size_mb": 1000,  # 最大缓存大小1GB
    "cleanup_interval_hours": 24,  # 24小时清理一次
    "access_tracker_file": "access_tracker.json"
}

# 通用缓存配置（向后兼容）
CACHE_CONFIG = ADJUSTED_CACHE_CONFIG

# ===== 其他配置 =====

# 是否允许在非交易时段执行交易
ALLOW_TRADE_IN_CLOSED_MARKET = False

# 系统初始化函数，创建必要的目录等
def initialize_system():
    """初始化系统配置，创建必要的目录"""
    # 确保关键目录存在
    directories_to_create = [
        LOG_DIR,
        BACKTEST_RESULT_DIR,
        CACHE_DIR,
        ADJUSTED_CACHE_CONFIG["cache_dir"],
        CONTINUOUS_CACHE_CONFIG["cache_dir"]
    ]

    for directory in directories_to_create:
        os.makedirs(directory, exist_ok=True)

    # 简单日志输出
    print("系统初始化完成")
    print(f"数据根目录: {DATA_ROOT}")
    print(f"缓存目录: {CACHE_DIR}")
    print(f"股票复权缓存: {ADJUSTED_CACHE_CONFIG['cache_dir']}")
    print(f"期货连续化缓存: {CONTINUOUS_CACHE_CONFIG['cache_dir']}")

    # 连接数据源等其他初始化工作
    pass


# 在导入时自动初始化系统(可选)
# if __name__ != "__main__":
#     initialize_system() 