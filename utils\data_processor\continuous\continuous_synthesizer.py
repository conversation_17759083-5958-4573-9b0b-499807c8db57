#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期货连续化数据合成器

提供期货连续化数据的统一合成接口，包括：
1. 主力连续数据合成
2. 加权连续数据合成
3. 缓存管理集成
4. 数据验证和错误处理

统一接口设计：
- 自动选择合适的连续化算法
- 智能缓存管理
- 数据完整性验证
- 错误处理和日志记录
"""

import pandas as pd
from typing import Optional, Dict, Any, Literal
from datetime import datetime

from .continuous_factor_storage import ContinuousFactorStorage
from .continuous_engine import ContinuousEngine
from utils.cache_manager import continuous_cache_manager
from utils.logger.manager import get_unified_logger

logger = get_unified_logger(__name__)


class ContinuousSynthesizer:
    """期货连续化数据合成器"""
    
    def __init__(self):
        """初始化连续化数据合成器"""
        self.factor_storage = ContinuousFactorStorage()
        self.engine = ContinuousEngine()
        self.cache = continuous_cache_manager  # 使用智能缓存管理器

        logger.info("期货连续化数据合成器初始化完成")
    
    def synthesize_continuous_data(
        self,
        symbol: str,
        price_data: pd.DataFrame,
        continuous_type: Literal["main", "weighted"] = "main",
        method: Literal["ratio", "diff"] = "ratio",
        use_cache: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """合成连续化数据（统一接口）
        
        Args:
            symbol: 期货品种代码，如rb00.SF
            price_data: 原始价格数据
            continuous_type: 连续化类型，"main"（主力连续）或"weighted"（加权连续）
            method: 连续化方法，"ratio"（比例调整）或"diff"（差值调整）
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            连续化后的价格数据
        """
        try:
            logger.debug(f"开始合成连续化数据: {symbol}, 类型: {continuous_type}, 方法: {method}")
            
            # 数据验证
            if price_data.empty:
                logger.warning("输入价格数据为空")
                return pd.DataFrame()
            
            # 尝试从缓存获取
            if use_cache:
                cached_data = self.cache.get_cached_data(
                    symbol=symbol,
                    continuous_type=continuous_type,
                    method=method,
                    **kwargs
                )
                if cached_data is not None:
                    logger.debug(f"从缓存获取连续化数据: {symbol}")
                    return cached_data
            
            # 根据连续化类型选择处理方法
            if continuous_type == "main":
                result = self._synthesize_main_continuous(symbol, price_data, method)
            elif continuous_type == "weighted":
                result = self._synthesize_weighted_continuous(symbol, price_data, **kwargs)
            else:
                logger.error(f"不支持的连续化类型: {continuous_type}")
                return price_data.copy()
            
            # 数据验证
            if result.empty:
                logger.warning("连续化数据合成结果为空")
                return result
            
            # 存储到缓存
            if use_cache and not result.empty:
                self.cache.put_cached_data(
                    result,
                    symbol=symbol,
                    continuous_type=continuous_type,
                    method=method,
                    **kwargs
                )
            
            logger.info(f"连续化数据合成完成: {symbol}, 数据量: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"连续化数据合成失败: {e}")
            return price_data.copy()
    
    def _synthesize_main_continuous(
        self,
        symbol: str,
        price_data: pd.DataFrame,
        method: str
    ) -> pd.DataFrame:
        """合成主力连续数据
        
        Args:
            symbol: 期货品种代码
            price_data: 原始价格数据
            method: 连续化方法
            
        Returns:
            主力连续化后的数据
        """
        try:
            # 获取连续化因子
            continuous_factors = self.factor_storage.query_continuous_factors(symbol)
            
            if continuous_factors.empty:
                logger.info(f"未找到连续化因子数据: {symbol}，返回原始数据")
                return price_data.copy()
            
            # 执行主力连续化计算
            result = self.engine.calculate_main_continuous(price_data, continuous_factors, method)
            
            return result
            
        except Exception as e:
            logger.error(f"主力连续数据合成失败: {e}")
            return price_data.copy()
    
    def _synthesize_weighted_continuous(
        self,
        symbol: str,
        price_data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """合成加权连续数据
        
        Args:
            symbol: 期货品种代码
            price_data: 原始价格数据
            **kwargs: 其他参数，包括contracts_data和weights_data
            
        Returns:
            加权连续化后的数据
        """
        try:
            # 从kwargs获取必要的数据
            contracts_data = kwargs.get('contracts_data', {})
            weights_data = kwargs.get('weights_data', {})
            
            if not contracts_data or not weights_data:
                logger.warning("加权连续化需要contracts_data和weights_data参数")
                return price_data.copy()
            
            # 执行加权连续化计算
            result = self.engine.calculate_weighted_continuous(contracts_data, weights_data)
            
            return result
            
        except Exception as e:
            logger.error(f"加权连续数据合成失败: {e}")
            return price_data.copy()
    
    def update_continuous_factors(
        self,
        symbol: str,
        old_contract: str,
        new_contract: str,
        old_contract_data: pd.DataFrame,
        new_contract_data: pd.DataFrame,
        switch_date: datetime
    ) -> bool:
        """更新连续化因子
        
        Args:
            symbol: 期货品种代码
            old_contract: 旧主力合约代码
            new_contract: 新主力合约代码
            old_contract_data: 旧合约数据
            new_contract_data: 新合约数据
            switch_date: 切换日期
            
        Returns:
            更新是否成功
        """
        try:
            logger.info(f"更新连续化因子: {symbol}, {old_contract} -> {new_contract}")
            
            # 计算连续化因子
            factors = self.engine.calculate_continuous_factors(
                old_contract_data, new_contract_data, switch_date
            )
            
            # 构建切换记录
            switch_record = {
                'switch_date': switch_date,
                'old_contract': old_contract,
                'new_contract': new_contract,
                'old_price': self.engine._get_price_at_date(old_contract_data, switch_date),
                'new_price': self.engine._get_price_at_date(new_contract_data, switch_date),
                'ratio_factor': factors['ratio_factor'],
                'diff_factor': factors['diff_factor']
            }
            
            # 保存到存储
            success = self.factor_storage.update_continuous_factors(symbol, switch_record)
            
            if success:
                logger.info(f"连续化因子更新成功: {symbol}")
                # 清理相关缓存
                self._invalidate_cache(symbol)
            else:
                logger.error(f"连续化因子更新失败: {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新连续化因子失败: {e}")
            return False
    
    def _invalidate_cache(self, symbol: str):
        """使指定品种的缓存失效
        
        Args:
            symbol: 期货品种代码
        """
        try:
            # 获取所有相关的缓存键
            cache_keys_to_remove = []
            for cache_key, info in self.cache.access_tracker.items():
                cache_params = info.get('cache_params', {})
                if cache_params.get('symbol') == symbol:
                    cache_keys_to_remove.append(cache_key)

            # 删除相关缓存
            for cache_key in cache_keys_to_remove:
                self.cache._remove_cache(cache_key)

            if cache_keys_to_remove:
                self.cache._save_access_tracker()
                logger.info(f"已清理 {symbol} 相关缓存，数量: {len(cache_keys_to_remove)}")

        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
    
    def get_continuous_info(self, symbol: str) -> Dict[str, Any]:
        """获取连续化信息
        
        Args:
            symbol: 期货品种代码
            
        Returns:
            连续化信息字典
        """
        try:
            # 获取最新切换信息
            latest_switch = self.factor_storage.get_latest_switch_info(symbol)
            
            # 获取所有连续化因子
            all_factors = self.factor_storage.query_continuous_factors(symbol)
            
            # 获取缓存统计
            cache_stats = self.cache.get_cache_statistics()
            
            return {
                'symbol': symbol,
                'latest_switch': latest_switch,
                'total_switches': len(all_factors),
                'cache_statistics': cache_stats
            }
            
        except Exception as e:
            logger.error(f"获取连续化信息失败: {e}")
            return {}
    
    def cleanup_cache(self) -> int:
        """清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        try:
            return self.cache.cleanup_expired_cache()
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
            return 0
