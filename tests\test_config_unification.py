#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置统一重构验证测试

验证配置重构后系统能正常运行，配置访问正确
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestConfigUnification(unittest.TestCase):
    """配置统一重构验证测试"""
    
    def test_settings_import(self):
        """测试统一配置文件导入"""
        try:
            from config.settings import (
                LOG_LEVEL, LOG_DIR, DATA_ROOT, DEFAULT_TIMEZONE,
                DATA_VALIDATION_LEVEL, ERROR_HANDLING_STRATEGY,
                TIME_CONVERTER_CACHE_SIZE, DEBUG_LEVEL
            )
            self.assertTrue(True, "配置导入成功")
        except ImportError as e:
            self.fail(f"配置导入失败: {e}")
    
    def test_removed_config_files(self):
        """测试重复配置文件已被删除"""
        removed_files = [
            "config/system_config.json",
            "utils/config/config_manager.py", 
            "utils/time_converter_config.py",
            "config/data_source_config.py"
        ]
        
        for file_path in removed_files:
            full_path = project_root / file_path
            self.assertFalse(full_path.exists(), f"重复配置文件应该被删除: {file_path}")
    
    def test_backup_files_exist(self):
        """测试备份文件存在"""
        backup_files = [
            "config/system_config.json.backup",
            "utils/config/config_manager.py.backup",
            "utils/time_converter_config.py.backup", 
            "config/data_source_config.py.backup"
        ]
        
        for file_path in backup_files:
            full_path = project_root / file_path
            self.assertTrue(full_path.exists(), f"备份文件应该存在: {file_path}")
    
    def test_logger_config_unified(self):
        """测试日志配置统一"""
        try:
            from utils.logger import setup_unified_logging, LogTarget
            from config.settings import LOG_LEVEL, DEBUG_LOG_ENABLED
            
            # 测试日志配置能正常使用
            setup_unified_logging(
                log_level=LOG_LEVEL,
                default_target=LogTarget.FILE,
                enable_debug_log=DEBUG_LOG_ENABLED
            )
            self.assertTrue(True, "日志配置统一成功")
        except Exception as e:
            self.fail(f"日志配置统一失败: {e}")
    
    def test_time_converter_config_unified(self):
        """测试时间转换器配置统一"""
        try:
            from config.settings import (
                TIME_CONVERTER_CACHE_SIZE, TIME_CONVERTER_ENABLE_MONITORING,
                TIME_CONVERTER_ENABLE_WARNINGS, DEFAULT_TIMEZONE
            )
            
            # 验证配置值合理
            self.assertIsInstance(TIME_CONVERTER_CACHE_SIZE, int)
            self.assertIsInstance(TIME_CONVERTER_ENABLE_MONITORING, bool)
            self.assertEqual(DEFAULT_TIMEZONE, "Asia/Shanghai")
            
        except ImportError as e:
            self.fail(f"时间转换器配置统一失败: {e}")
    
    def test_data_processing_config_unified(self):
        """测试数据处理配置统一"""
        try:
            from config.settings import (
                DATA_VALIDATION_LEVEL, ERROR_HANDLING_STRATEGY,
                AUTO_FIX_TYPES, STRICT_TYPE_CHECKING,
                ENABLE_PERFORMANCE_MONITORING
            )
            
            # 验证配置值合理
            self.assertIn(DATA_VALIDATION_LEVEL, ["strict", "normal", "loose"])
            self.assertIn(ERROR_HANDLING_STRATEGY, ["stop", "continue", "retry"])
            self.assertIsInstance(AUTO_FIX_TYPES, bool)
            
        except ImportError as e:
            self.fail(f"数据处理配置统一失败: {e}")
    
    def test_no_config_manager_references(self):
        """测试不再有config_manager引用"""
        # 检查关键文件中不再有config_manager导入
        files_to_check = [
            "data/storage/parquet_reader.py",
            "utils/time_converter_manager.py"
        ]
        
        for file_path in files_to_check:
            full_path = project_root / file_path
            if full_path.exists():
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.assertNotIn("from utils.config.config_manager import", content,
                                   f"文件 {file_path} 不应该再导入config_manager")
    
    def test_system_initialization(self):
        """测试系统初始化正常"""
        try:
            from config.settings import initialize_system
            initialize_system()
            self.assertTrue(True, "系统初始化成功")
        except Exception as e:
            self.fail(f"系统初始化失败: {e}")


if __name__ == "__main__":
    print("开始配置统一重构验证测试...")
    unittest.main(verbosity=2)
