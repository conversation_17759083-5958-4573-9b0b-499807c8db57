#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引性能对比测试

测试字符串索引 vs 数值索引 vs DatetimeIndex的性能差异
"""

import pandas as pd
import numpy as np
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)


def test_index_performance():
    """测试不同索引类型的性能"""
    
    # 创建测试数据
    n = 1000000  # 100万行数据
    logger.info(f"测试数据量: {n:,} 行")
    
    # 生成时间戳数据
    timestamps = pd.date_range('2024-01-01 09:30:00', periods=n, freq='1s')
    
    # 测试1: 字符串索引
    logger.info("\n=== 字符串索引性能测试 ===")
    df_str = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=timestamps.strftime('%Y%m%d%H%M%S'))
    
    # 字符串索引查询性能
    start_time = time.time()
    result1 = df_str.loc['20240101093000':'20240101100000']
    str_query_time = time.time() - start_time
    logger.info(f"字符串索引查询时间: {str_query_time:.6f}秒, 结果行数: {len(result1)}")
    
    # 测试2: 数值索引
    logger.info("\n=== 数值索引性能测试 ===")
    numeric_index = (timestamps - pd.Timestamp('1970-01-01')) // pd.Timedelta('1ms')
    df_num = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=numeric_index)
    
    # 数值索引查询性能
    start_ts = int(pd.Timestamp('2024-01-01 09:30:00').timestamp() * 1000)
    end_ts = int(pd.Timestamp('2024-01-01 10:00:00').timestamp() * 1000)
    start_time = time.time()
    result2 = df_num.loc[start_ts:end_ts]
    num_query_time = time.time() - start_time
    logger.info(f"数值索引查询时间: {num_query_time:.6f}秒, 结果行数: {len(result2)}")
    
    # 测试3: DatetimeIndex
    logger.info("\n=== DatetimeIndex性能测试 ===")
    df_dt = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=timestamps)
    
    # DatetimeIndex查询性能
    start_time = time.time()
    result3 = df_dt.loc['2024-01-01 09:30:00':'2024-01-01 10:00:00']
    dt_query_time = time.time() - start_time
    logger.info(f"DatetimeIndex查询时间: {dt_query_time:.6f}秒, 结果行数: {len(result3)}")
    
    # 性能对比
    logger.info("\n=== 性能对比总结 ===")
    logger.info(f"字符串索引: {str_query_time:.6f}秒 (基准)")
    logger.info(f"数值索引: {num_query_time:.6f}秒 ({num_query_time/str_query_time:.2f}倍)")
    logger.info(f"DatetimeIndex: {dt_query_time:.6f}秒 ({dt_query_time/str_query_time:.2f}倍)")
    
    # 内存使用对比
    logger.info("\n=== 内存使用对比 ===")
    str_memory = df_str.memory_usage(deep=True).sum() / 1024 / 1024
    num_memory = df_num.memory_usage(deep=True).sum() / 1024 / 1024
    dt_memory = df_dt.memory_usage(deep=True).sum() / 1024 / 1024
    
    logger.info(f"字符串索引内存: {str_memory:.2f} MB")
    logger.info(f"数值索引内存: {num_memory:.2f} MB")
    logger.info(f"DatetimeIndex内存: {dt_memory:.2f} MB")
    
    # 测试多次查询的累积性能
    logger.info("\n=== 多次查询累积性能测试 ===")
    query_count = 100
    
    # 字符串索引多次查询
    start_time = time.time()
    for i in range(query_count):
        start_idx = f"2024010109{30+i%30:02d}00"
        end_idx = f"2024010109{35+i%25:02d}00"
        _ = df_str.loc[start_idx:end_idx]
    str_multi_time = time.time() - start_time
    
    # 数值索引多次查询
    start_time = time.time()
    for i in range(query_count):
        start_ts = int(pd.Timestamp(f'2024-01-01 09:{30+i%30:02d}:00').timestamp() * 1000)
        end_ts = int(pd.Timestamp(f'2024-01-01 09:{35+i%25:02d}:00').timestamp() * 1000)
        _ = df_num.loc[start_ts:end_ts]
    num_multi_time = time.time() - start_time
    
    # DatetimeIndex多次查询
    start_time = time.time()
    for i in range(query_count):
        start_dt = f'2024-01-01 09:{30+i%30:02d}:00'
        end_dt = f'2024-01-01 09:{35+i%25:02d}:00'
        _ = df_dt.loc[start_dt:end_dt]
    dt_multi_time = time.time() - start_time
    
    logger.info(f"字符串索引{query_count}次查询: {str_multi_time:.6f}秒")
    logger.info(f"数值索引{query_count}次查询: {num_multi_time:.6f}秒 ({num_multi_time/str_multi_time:.2f}倍)")
    logger.info(f"DatetimeIndex{query_count}次查询: {dt_multi_time:.6f}秒 ({dt_multi_time/str_multi_time:.2f}倍)")
    
    return {
        'single_query': {
            'string': str_query_time,
            'numeric': num_query_time,
            'datetime': dt_query_time
        },
        'memory_usage': {
            'string': str_memory,
            'numeric': num_memory,
            'datetime': dt_memory
        },
        'multi_query': {
            'string': str_multi_time,
            'numeric': num_multi_time,
            'datetime': dt_multi_time
        }
    }


def test_small_data_performance():
    """测试小数据量的性能差异"""
    logger.info("\n=== 小数据量性能测试 (10万行) ===")
    
    n = 100000  # 10万行数据
    timestamps = pd.date_range('2024-01-01 09:30:00', periods=n, freq='1s')
    
    # 创建不同索引类型的DataFrame
    df_str = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=timestamps.strftime('%Y%m%d%H%M%S'))
    
    numeric_index = (timestamps - pd.Timestamp('1970-01-01')) // pd.Timedelta('1ms')
    df_num = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=numeric_index)
    
    df_dt = pd.DataFrame({
        'price': np.random.randn(n) + 100,
        'volume': np.random.randint(1000, 10000, n)
    }, index=timestamps)
    
    # 测试查询性能
    query_count = 1000
    
    # 字符串索引
    start_time = time.time()
    for i in range(query_count):
        start_idx = f"2024010109{30+i%30:02d}{i%60:02d}"
        end_idx = f"2024010109{35+i%25:02d}{(i+10)%60:02d}"
        _ = df_str.loc[start_idx:end_idx]
    str_time = time.time() - start_time
    
    # 数值索引
    start_time = time.time()
    for i in range(query_count):
        start_ts = int(pd.Timestamp(f'2024-01-01 09:{30+i%30:02d}:{i%60:02d}').timestamp() * 1000)
        end_ts = int(pd.Timestamp(f'2024-01-01 09:{35+i%25:02d}:{(i+10)%60:02d}').timestamp() * 1000)
        _ = df_num.loc[start_ts:end_ts]
    num_time = time.time() - start_time
    
    # DatetimeIndex
    start_time = time.time()
    for i in range(query_count):
        start_dt = f'2024-01-01 09:{30+i%30:02d}:{i%60:02d}'
        end_dt = f'2024-01-01 09:{35+i%25:02d}:{(i+10)%60:02d}'
        _ = df_dt.loc[start_dt:end_dt]
    dt_time = time.time() - start_time
    
    logger.info(f"小数据量{query_count}次查询:")
    logger.info(f"字符串索引: {str_time:.6f}秒")
    logger.info(f"数值索引: {num_time:.6f}秒 ({num_time/str_time:.2f}倍)")
    logger.info(f"DatetimeIndex: {dt_time:.6f}秒 ({dt_time/str_time:.2f}倍)")


if __name__ == "__main__":
    logger.info("开始索引性能对比测试")
    
    # 大数据量测试
    results = test_index_performance()
    
    # 小数据量测试
    test_small_data_performance()
    
    logger.info("\n=== 测试结论 ===")
    logger.info("1. 单次查询性能: DatetimeIndex > 数值索引 > 字符串索引")
    logger.info("2. 内存使用: 数值索引 < DatetimeIndex < 字符串索引")
    logger.info("3. 多次查询累积性能差异更明显")
    logger.info("4. 小数据量下性能差异相对较小")
    
    logger.info("索引性能对比测试完成")
