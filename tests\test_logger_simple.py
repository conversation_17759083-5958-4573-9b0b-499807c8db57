#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单日志测试
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

if __name__ == "__main__":
    print("测试日志系统...")
    
    try:
        from utils.logger import get_unified_logger
        logger = get_unified_logger('test')
        logger.info('测试日志系统')
        print("✅ 日志系统测试成功")
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
