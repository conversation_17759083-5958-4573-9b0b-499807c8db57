#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
复权数据质量监控器

提供复权前后数据质量的自动检测和验证功能，确保复权处理的正确性。
遵循"宁可报错也不掩盖bug"的原则，及时发现和报告数据质量问题。

监控内容：
1. 数量字段是否保持整数特性
2. 价格字段复权调整的合理性
3. 数据类型变化检测
4. 复权前后数据一致性校验
5. 异常数据自动报警
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
from utils.logger import get_unified_logger, LogTarget
from .field_type_classifier import field_classifier, FieldType

logger = get_unified_logger(__name__)


class QualityIssueLevel(Enum):
    """数据质量问题级别"""
    INFO = "info"           # 信息级别
    WARNING = "warning"     # 警告级别
    ERROR = "error"         # 错误级别
    CRITICAL = "critical"   # 严重错误级别


class QualityIssue:
    """数据质量问题记录"""
    
    def __init__(self, level: QualityIssueLevel, field: str, message: str, 
                 details: Optional[Dict[str, Any]] = None):
        self.level = level
        self.field = field
        self.message = message
        self.details = details or {}
        self.timestamp = pd.Timestamp.now()
    
    def __str__(self):
        return f"[{self.level.value.upper()}] {self.field}: {self.message}"


class AdjustmentDataQualityMonitor:
    """复权数据质量监控器"""
    
    def __init__(self):
        """初始化数据质量监控器"""
        self.issues: List[QualityIssue] = []
        logger.debug(LogTarget.FILE, "复权数据质量监控器初始化完成")
    
    def clear_issues(self):
        """清空问题记录"""
        self.issues.clear()
        logger.debug(LogTarget.FILE, "数据质量问题记录已清空")
    
    def add_issue(self, level: QualityIssueLevel, field: str, message: str, 
                  details: Optional[Dict[str, Any]] = None):
        """添加数据质量问题"""
        issue = QualityIssue(level, field, message, details)
        self.issues.append(issue)
        
        # 根据级别记录日志
        log_message = str(issue)
        if level == QualityIssueLevel.CRITICAL:
            logger.error(log_message)
        elif level == QualityIssueLevel.ERROR:
            logger.error(log_message)
        elif level == QualityIssueLevel.WARNING:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def monitor_adjustment_quality(self, original_data: pd.DataFrame, 
                                 adjusted_data: pd.DataFrame) -> bool:
        """
        监控复权前后数据质量
        
        Args:
            original_data: 原始数据
            adjusted_data: 复权后数据
            
        Returns:
            bool: 数据质量是否合格
        """
        self.clear_issues()
        
        logger.info(LogTarget.FILE, f"开始监控复权数据质量，原始数据: {len(original_data)} 行，复权后: {len(adjusted_data)} 行")
        
        # 基础检查
        if not self._check_basic_consistency(original_data, adjusted_data):
            return False
        
        # 统一执行字段分类（只执行一次）
        field_types = field_classifier.classify_dataframe_fields(original_data)

        # 字段类型检查（复用分类结果）
        self._check_field_types_with_result(original_data, adjusted_data, field_types)

        # 数量字段保持性检查（复用分类结果）
        self._check_volume_field_preservation(original_data, adjusted_data, field_types)

        # 价格字段复权合理性检查（复用分类结果）
        self._check_price_field_adjustment(original_data, adjusted_data, field_types)
        
        # 数据类型变化检查
        self._check_data_type_changes(original_data, adjusted_data)
        
        # 异常值检查
        self._check_abnormal_values(adjusted_data)
        
        # 汇总检查结果
        return self._summarize_quality_check()
    
    def _check_basic_consistency(self, original_data: pd.DataFrame, 
                               adjusted_data: pd.DataFrame) -> bool:
        """检查基础一致性"""
        # 检查行数是否一致
        if len(original_data) != len(adjusted_data):
            self.add_issue(
                QualityIssueLevel.CRITICAL,
                "data_shape",
                f"数据行数不一致：原始 {len(original_data)} 行，复权后 {len(adjusted_data)} 行"
            )
            return False
        
        # 检查列数是否一致
        if len(original_data.columns) != len(adjusted_data.columns):
            self.add_issue(
                QualityIssueLevel.CRITICAL,
                "data_shape",
                f"数据列数不一致：原始 {len(original_data.columns)} 列，复权后 {len(adjusted_data.columns)} 列"
            )
            return False
        
        # 检查列名是否一致
        if not original_data.columns.equals(adjusted_data.columns):
            self.add_issue(
                QualityIssueLevel.ERROR,
                "column_names",
                "列名不一致",
                {
                    "original_columns": list(original_data.columns),
                    "adjusted_columns": list(adjusted_data.columns)
                }
            )
            return False
        
        return True
    
    def _check_field_types_with_result(self, original_data: pd.DataFrame,
                                     adjusted_data: pd.DataFrame,
                                     field_types: Dict[str, FieldType]):
        """检查字段类型分类（使用已有分类结果）"""
        # 检查是否有未知字段
        unknown_fields = [field for field, ftype in field_types.items()
                         if ftype == FieldType.UNKNOWN_FIELD]

        if unknown_fields:
            self.add_issue(
                QualityIssueLevel.WARNING,
                "field_classification",
                f"发现未知字段类型: {unknown_fields}",
                {"unknown_fields": unknown_fields}
            )

        # 检查是否有价格字段（基于已分类的结果）
        price_fields = [field for field, ftype in field_types.items()
                       if ftype == FieldType.PRICE_FIELD]
        if not price_fields:
            self.add_issue(
                QualityIssueLevel.WARNING,
                "field_classification",
                "未发现任何价格字段，可能不需要复权处理"
            )
    
    def _check_volume_field_preservation(self, original_data: pd.DataFrame,
                                       adjusted_data: pd.DataFrame,
                                       field_types: Dict[str, FieldType] = None):
        """检查数量字段是否保持不变"""
        # 复用已有的字段分类结果，避免重复分类
        if field_types is None:
            field_types = field_classifier.classify_dataframe_fields(original_data)

        # 获取数量相关字段
        volume_fields = [field for field, ftype in field_types.items()
                        if ftype in [FieldType.VOLUME_FIELD, FieldType.COUNT_FIELD, FieldType.TIME_FIELD]]
        
        for field in volume_fields:
            if field in original_data.columns and field in adjusted_data.columns:
                original_series = original_data[field]
                adjusted_series = adjusted_data[field]
                
                # 检查是否完全相等
                if not original_series.equals(adjusted_series):
                    # 详细检查差异
                    diff_count = 0
                    for i in range(len(original_series)):
                        if not self._values_equal(original_series.iloc[i], adjusted_series.iloc[i]):
                            diff_count += 1
                    
                    self.add_issue(
                        QualityIssueLevel.ERROR,
                        field,
                        f"数量字段发生了意外变化，{diff_count} 行数据不一致",
                        {
                            "field_type": field_types[field].value,
                            "diff_count": diff_count,
                            "total_rows": len(original_series)
                        }
                    )
    
    def _check_price_field_adjustment(self, original_data: pd.DataFrame,
                                    adjusted_data: pd.DataFrame,
                                    field_types: Dict[str, FieldType] = None):
        """检查价格字段复权调整的合理性"""
        # 复用已有的字段分类结果，避免重复分类
        if field_types is None:
            field_types = field_classifier.classify_dataframe_fields(original_data)

        price_fields = [field for field, ftype in field_types.items()
                       if ftype == FieldType.PRICE_FIELD]
        
        for field in price_fields:
            if field in original_data.columns and field in adjusted_data.columns:
                original_series = original_data[field]
                adjusted_series = adjusted_data[field]
                
                # 检查是否发生了变化（价格字段应该发生变化）
                if original_series.equals(adjusted_series):
                    # 对于结算价字段，降级为INFO级别（期货结算价通常不需要复权）
                    if field.lower() in ['settlementprice', 'lastsettlementprice', 'settlement']:
                        self.add_issue(
                            QualityIssueLevel.INFO,
                            field,
                            "结算价字段未发生复权调整，这在期货数据中是正常的"
                        )
                    else:
                        self.add_issue(
                            QualityIssueLevel.WARNING,
                            field,
                            "价格字段未发生复权调整，可能复权因子为空或全为1"
                        )
                else:
                    # 检查调整的合理性
                    self._check_price_adjustment_reasonableness(field, original_series, adjusted_series)
    
    def _check_price_adjustment_reasonableness(self, field: str, 
                                             original_series: pd.Series, 
                                             adjusted_series: pd.Series):
        """检查价格调整的合理性"""
        # 对于数组字段（如bidPrice, askPrice）
        if self._is_array_field(original_series):
            self._check_array_price_adjustment(field, original_series, adjusted_series)
        else:
            # 普通数值字段
            self._check_numeric_price_adjustment(field, original_series, adjusted_series)
    
    def _check_array_price_adjustment(self, field: str, 
                                    original_series: pd.Series, 
                                    adjusted_series: pd.Series):
        """检查数组价格字段的调整合理性"""
        for i in range(len(original_series)):
            original_array = original_series.iloc[i]
            adjusted_array = adjusted_series.iloc[i]
            
            if isinstance(original_array, (list, tuple, np.ndarray)) and \
               isinstance(adjusted_array, (list, tuple, np.ndarray)):
                
                # 检查数组长度是否一致
                if len(original_array) != len(adjusted_array):
                    self.add_issue(
                        QualityIssueLevel.ERROR,
                        field,
                        f"数组长度不一致，行 {i}: 原始 {len(original_array)}，复权后 {len(adjusted_array)}"
                    )
                    continue
                
                # 检查0值是否保持不变
                for j, (orig_val, adj_val) in enumerate(zip(original_array, adjusted_array)):
                    if orig_val == 0 and adj_val != 0:
                        self.add_issue(
                            QualityIssueLevel.ERROR,
                            field,
                            f"0值发生了变化，行 {i} 位置 {j}: {orig_val} -> {adj_val}"
                        )
    
    def _check_numeric_price_adjustment(self, field: str, 
                                      original_series: pd.Series, 
                                      adjusted_series: pd.Series):
        """检查数值价格字段的调整合理性"""
        # 计算调整比例
        non_zero_mask = original_series != 0
        if non_zero_mask.any():
            ratios = adjusted_series[non_zero_mask] / original_series[non_zero_mask]
            
            # 检查调整比例是否合理（通常在0.5-2.0之间）
            extreme_ratios = (ratios < 0.1) | (ratios > 10.0)
            if extreme_ratios.any():
                extreme_count = extreme_ratios.sum()
                self.add_issue(
                    QualityIssueLevel.WARNING,
                    field,
                    f"发现 {extreme_count} 个极端复权比例（<0.1 或 >10.0）",
                    {
                        "min_ratio": float(ratios.min()),
                        "max_ratio": float(ratios.max()),
                        "extreme_count": int(extreme_count)
                    }
                )
    
    def _check_data_type_changes(self, original_data: pd.DataFrame, 
                                adjusted_data: pd.DataFrame):
        """检查数据类型变化"""
        for col in original_data.columns:
            original_dtype = original_data[col].dtype
            adjusted_dtype = adjusted_data[col].dtype
            
            if original_dtype != adjusted_dtype:
                # 某些类型变化是可接受的
                acceptable_changes = [
                    ('int64', 'float64'),  # 整数到浮点数（价格字段）
                    ('int32', 'float64'),
                    ('float32', 'float64')
                ]
                
                change_acceptable = any(
                    str(original_dtype) == old and str(adjusted_dtype) == new
                    for old, new in acceptable_changes
                )
                
                level = QualityIssueLevel.INFO if change_acceptable else QualityIssueLevel.WARNING
                self.add_issue(
                    level,
                    col,
                    f"数据类型发生变化: {original_dtype} -> {adjusted_dtype}"
                )
    
    def _check_abnormal_values(self, adjusted_data: pd.DataFrame):
        """检查异常值"""
        for col in adjusted_data.columns:
            series = adjusted_data[col]
            
            # 检查无穷大值
            if series.dtype in ['float64', 'float32']:
                inf_count = np.isinf(series).sum()
                if inf_count > 0:
                    self.add_issue(
                        QualityIssueLevel.ERROR,
                        col,
                        f"发现 {inf_count} 个无穷大值"
                    )
                
                # 检查NaN值
                nan_count = series.isna().sum()
                if nan_count > 0:
                    self.add_issue(
                        QualityIssueLevel.WARNING,
                        col,
                        f"发现 {nan_count} 个NaN值"
                    )
    
    def _is_array_field(self, series: pd.Series) -> bool:
        """检查是否为数组字段"""
        if series.empty:
            return False
        
        for value in series.dropna():
            if isinstance(value, (list, tuple, np.ndarray)):
                return True
            break
        
        return False
    
    def _values_equal(self, val1, val2) -> bool:
        """比较两个值是否相等（支持数组）"""
        if isinstance(val1, (list, tuple, np.ndarray)) and isinstance(val2, (list, tuple, np.ndarray)):
            return np.array_equal(val1, val2)
        else:
            return val1 == val2
    
    def _summarize_quality_check(self) -> bool:
        """汇总质量检查结果"""
        if not self.issues:
            logger.info(LogTarget.FILE, "✅ 复权数据质量检查通过，未发现问题")
            return True
        
        # 统计问题级别
        level_counts = {}
        for issue in self.issues:
            level_counts[issue.level] = level_counts.get(issue.level, 0) + 1
        
        logger.info(LogTarget.FILE, f"复权数据质量检查完成，发现 {len(self.issues)} 个问题:")
        for level, count in level_counts.items():
            logger.info(LogTarget.FILE, f"  {level.value}: {count} 个")
        
        # 如果有严重错误或错误，返回False
        has_critical_issues = any(
            issue.level in [QualityIssueLevel.CRITICAL, QualityIssueLevel.ERROR]
            for issue in self.issues
        )
        
        return not has_critical_issues
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取质量检查报告"""
        level_counts = {}
        field_issues = {}
        
        for issue in self.issues:
            # 统计级别
            level_counts[issue.level.value] = level_counts.get(issue.level.value, 0) + 1
            
            # 按字段分组
            if issue.field not in field_issues:
                field_issues[issue.field] = []
            field_issues[issue.field].append({
                "level": issue.level.value,
                "message": issue.message,
                "details": issue.details,
                "timestamp": issue.timestamp.isoformat()
            })
        
        return {
            "total_issues": len(self.issues),
            "level_counts": level_counts,
            "field_issues": field_issues,
            "has_critical_issues": any(
                issue.level in [QualityIssueLevel.CRITICAL, QualityIssueLevel.ERROR]
                for issue in self.issues
            )
        }


# 全局质量监控器实例
quality_monitor = AdjustmentDataQualityMonitor()
