#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能缓存管理器

提供统一的智能缓存管理功能，包括：
1. 基于使用频率的智能缓存失效机制
2. 7天未使用延时删除策略
3. 缓存大小监控和自动清理
4. 访问时间跟踪和统计

智能缓存策略：
- 访问时间跟踪：记录每次缓存访问的时间
- 延时删除：7天未使用才删除，而不是固定TTL
- 使用频率统计：提供缓存使用情况分析
- 自动清理：定期清理过期缓存和大小超限缓存
"""

import os
import hashlib
import pandas as pd
import threading
import time
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
import json
import schedule

from config.settings import ADJUSTED_CACHE_CONFIG, CONTINUOUS_CACHE_CONFIG
from utils.logger.manager import get_unified_logger

logger = get_unified_logger(__name__)


class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_config: Dict[str, Any]):
        """初始化智能缓存管理器
        
        Args:
            cache_config: 缓存配置字典
        """
        self.cache_dir = Path(cache_config["cache_dir"])
        self.unused_days_threshold = cache_config["unused_days_threshold"]
        self.max_cache_size_mb = cache_config["max_cache_size_mb"]
        self.cleanup_interval_hours = cache_config["cleanup_interval_hours"]
        self.access_tracker_file = self.cache_dir / cache_config["access_tracker_file"]
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载访问跟踪数据
        self.access_tracker = self._load_access_tracker()
        self._lock = threading.Lock()
        
        # 启动定期清理任务
        self._setup_cleanup_scheduler()
        
        logger.info(f"智能缓存管理器初始化，目录: {self.cache_dir}, 未使用阈值: {self.unused_days_threshold}天")
    
    def _load_access_tracker(self) -> Dict[str, Dict[str, Any]]:
        """加载访问跟踪数据"""
        try:
            if self.access_tracker_file.exists():
                with open(self.access_tracker_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换时间字符串为datetime对象
                    for cache_key, info in data.items():
                        if 'last_access' in info:
                            info['last_access'] = datetime.fromisoformat(info['last_access'])
                        if 'created_time' in info:
                            info['created_time'] = datetime.fromisoformat(info['created_time'])
                    return data
            else:
                return {}
        except Exception as e:
            logger.error(f"加载访问跟踪数据失败: {e}")
            return {}
    
    def _save_access_tracker(self):
        """保存访问跟踪数据"""
        try:
            with self._lock:
                # 转换datetime对象为字符串
                data_to_save = {}
                for cache_key, info in self.access_tracker.items():
                    data_to_save[cache_key] = info.copy()
                    if 'last_access' in data_to_save[cache_key]:
                        data_to_save[cache_key]['last_access'] = data_to_save[cache_key]['last_access'].isoformat()
                    if 'created_time' in data_to_save[cache_key]:
                        data_to_save[cache_key]['created_time'] = data_to_save[cache_key]['created_time'].isoformat()
                
                with open(self.access_tracker_file, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            logger.error(f"保存访问跟踪数据失败: {e}")
    
    def _generate_cache_key(self, **kwargs) -> str:
        """生成缓存键"""
        # 构建缓存键字符串
        key_parts = []
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}={value}")
        
        key_string = "|".join(key_parts)
        
        # 生成MD5哈希
        cache_key = hashlib.md5(key_string.encode('utf-8')).hexdigest()
        
        logger.debug(f"生成缓存键: {key_string} -> {cache_key}")
        return cache_key
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.parquet"
    
    def get_cached_data(self, **kwargs) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        try:
            cache_key = self._generate_cache_key(**kwargs)
            cache_file = self._get_cache_file_path(cache_key)
            
            if not cache_file.exists():
                logger.debug(f"缓存文件不存在: {cache_key}")
                return None
            
            # 检查是否过期（基于使用频率）
            if self._is_cache_expired(cache_key):
                logger.info(f"缓存已过期，删除: {cache_key}")
                self._remove_cache(cache_key)
                return None
            
            # 读取缓存数据
            cached_data = pd.read_parquet(cache_file)
            
            # 更新访问记录
            self._update_access_record(cache_key, kwargs)
            
            logger.debug(f"缓存命中: {cache_key}, 数据量: {len(cached_data)}")
            return cached_data
            
        except Exception as e:
            logger.error(f"获取缓存数据失败: {e}")
            return None
    
    def put_cached_data(self, data: pd.DataFrame, **kwargs) -> bool:
        """存储缓存数据"""
        try:
            cache_key = self._generate_cache_key(**kwargs)
            cache_file = self._get_cache_file_path(cache_key)
            
            # 保存数据为Parquet格式
            data.to_parquet(cache_file)
            
            # 记录缓存信息
            current_time = datetime.now()
            with self._lock:
                self.access_tracker[cache_key] = {
                    'created_time': current_time,
                    'last_access': current_time,
                    'access_count': 1,
                    'data_size': len(data),
                    'cache_params': kwargs
                }
            
            # 保存访问跟踪数据
            self._save_access_tracker()
            
            logger.debug(f"缓存数据已存储: {cache_key}, 数据量: {len(data)}")
            return True
            
        except Exception as e:
            logger.error(f"存储缓存数据失败: {e}")
            return False
    
    def _is_cache_expired(self, cache_key: str) -> bool:
        """检查缓存是否过期（基于使用频率）"""
        try:
            if cache_key not in self.access_tracker:
                return True
            
            last_access = self.access_tracker[cache_key]['last_access']
            days_unused = (datetime.now() - last_access).days
            
            # 7天未使用则认为过期
            return days_unused >= self.unused_days_threshold
            
        except Exception as e:
            logger.error(f"检查缓存过期状态失败: {e}")
            return True
    
    def _update_access_record(self, cache_key: str, cache_params: Dict[str, Any]):
        """更新访问记录"""
        try:
            with self._lock:
                if cache_key in self.access_tracker:
                    self.access_tracker[cache_key]['last_access'] = datetime.now()
                    self.access_tracker[cache_key]['access_count'] += 1
                    
                    # 定期保存访问跟踪数据（每10次访问保存一次）
                    if self.access_tracker[cache_key]['access_count'] % 10 == 0:
                        self._save_access_tracker()
                        
        except Exception as e:
            logger.error(f"更新访问记录失败: {e}")
    
    def _remove_cache(self, cache_key: str):
        """删除缓存"""
        try:
            cache_file = self._get_cache_file_path(cache_key)
            
            # 删除缓存文件
            if cache_file.exists():
                cache_file.unlink()
            
            # 删除访问记录
            with self._lock:
                if cache_key in self.access_tracker:
                    del self.access_tracker[cache_key]
            
            logger.debug(f"缓存已删除: {cache_key}")
            
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        try:
            expired_keys = []
            
            with self._lock:
                for cache_key in list(self.access_tracker.keys()):
                    if self._is_cache_expired(cache_key):
                        expired_keys.append(cache_key)
            
            # 删除过期缓存
            for cache_key in expired_keys:
                self._remove_cache(cache_key)
            
            # 保存更新后的访问跟踪数据
            if expired_keys:
                self._save_access_tracker()
            
            logger.info(f"清理过期缓存完成，清理数量: {len(expired_keys)}")
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
            return 0
    
    def cleanup_oversized_cache(self) -> int:
        """清理超大缓存"""
        try:
            # 计算当前缓存大小
            current_size_mb = self._get_cache_size_mb()
            
            if current_size_mb <= self.max_cache_size_mb:
                return 0
            
            # 按访问时间排序，删除最久未访问的缓存
            with self._lock:
                sorted_caches = sorted(
                    self.access_tracker.items(),
                    key=lambda x: x[1]['last_access']
                )
            
            removed_count = 0
            for cache_key, _ in sorted_caches:
                self._remove_cache(cache_key)
                removed_count += 1
                
                # 重新计算大小
                current_size_mb = self._get_cache_size_mb()
                if current_size_mb <= self.max_cache_size_mb * 0.8:  # 清理到80%
                    break
            
            if removed_count > 0:
                self._save_access_tracker()
            
            logger.info(f"清理超大缓存完成，清理数量: {removed_count}")
            return removed_count
            
        except Exception as e:
            logger.error(f"清理超大缓存失败: {e}")
            return 0
    
    def _get_cache_size_mb(self) -> float:
        """获取缓存大小（MB）"""
        try:
            total_size = 0
            for cache_file in self.cache_dir.glob("*.parquet"):
                total_size += cache_file.stat().st_size
            return total_size / (1024 * 1024)
        except Exception as e:
            logger.error(f"计算缓存大小失败: {e}")
            return 0
    
    def _setup_cleanup_scheduler(self):
        """设置定期清理调度器"""
        def cleanup_task():
            logger.info("开始定期缓存清理任务")
            expired_count = self.cleanup_expired_cache()
            oversized_count = self.cleanup_oversized_cache()
            logger.info(f"定期缓存清理完成，过期: {expired_count}, 超大: {oversized_count}")
        
        # 设置定期清理任务
        schedule.every(self.cleanup_interval_hours).hours.do(cleanup_task)
        
        # 启动调度器线程
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        logger.info(f"缓存清理调度器已启动，间隔: {self.cleanup_interval_hours}小时")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with self._lock:
                total_caches = len(self.access_tracker)
                total_access_count = sum(info['access_count'] for info in self.access_tracker.values())
            
            cache_size_mb = self._get_cache_size_mb()
            
            return {
                'total_caches': total_caches,
                'total_access_count': total_access_count,
                'cache_size_mb': round(cache_size_mb, 2),
                'max_cache_size_mb': self.max_cache_size_mb,
                'unused_threshold_days': self.unused_days_threshold,
                'cache_directory': str(self.cache_dir)
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
            return {}


# 全局缓存管理器实例
adjusted_cache_manager = IntelligentCacheManager(ADJUSTED_CACHE_CONFIG)
continuous_cache_manager = IntelligentCacheManager(CONTINUOUS_CACHE_CONFIG)
