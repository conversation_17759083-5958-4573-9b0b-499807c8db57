"""
复权价格缓存管理器

该模块负责复权价格数据的智能缓存管理，包括：
1. 内存缓存（会话级别）
2. 磁盘缓存（临时存储）
3. LRU缓存淘汰策略
4. 缓存有效期管理
5. 性能监控和统计

注意：这里缓存的是复权价格数据（派生数据），不是复权因子数据（基础数据）
"""

import os
import pandas as pd
import pickle
import hashlib
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import threading
from collections import OrderedDict
import logging

from utils.logger import get_unified_logger

logger = get_unified_logger(__name__, enhanced=True)


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, capacity: int = 100):
        """初始化LRU缓存
        
        Args:
            capacity: 缓存容量
        """
        self.capacity = capacity
        self.cache = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def put(self, key: str, value: Any) -> None:
        """存储缓存数据"""
        with self.lock:
            if key in self.cache:
                # 更新现有数据
                self.cache.pop(key)
            elif len(self.cache) >= self.capacity:
                # 删除最久未使用的数据
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def remove(self, key: str) -> bool:
        """删除缓存数据"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> list:
        """获取所有缓存键"""
        with self.lock:
            return list(self.cache.keys())


class AdjustmentPriceCache:
    """复权价格缓存管理器
    
    负责复权价格数据的智能缓存管理，包括内存缓存和磁盘缓存的双层架构。
    复权价格作为派生数据，可以按需计算和缓存。
    """
    
    def __init__(
        self, 
        memory_capacity: int = 100,
        disk_cache_dir: Optional[str] = None,
        cache_ttl_hours: int = 24
    ):
        """初始化复权价格缓存管理器
        
        Args:
            memory_capacity: 内存缓存容量
            disk_cache_dir: 磁盘缓存目录
            cache_ttl_hours: 缓存有效期（小时）
        """
        self.memory_cache = LRUCache(memory_capacity)
        self.cache_ttl = timedelta(hours=cache_ttl_hours)
        
        # 磁盘缓存目录
        self.disk_cache_dir = Path(disk_cache_dir or "data/cache/adjustment_prices")
        self.disk_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存统计
        self.stats = {
            'memory_hits': 0,
            'memory_misses': 0,
            'disk_hits': 0,
            'disk_misses': 0,
            'cache_puts': 0,
            'cache_evictions': 0
        }
        
        logger.debug(f"复权价格缓存管理器初始化完成，内存容量: {memory_capacity}, 磁盘目录: {self.disk_cache_dir}")
    
    def _generate_cache_key(
        self, 
        stock_code: str, 
        start_date: str, 
        end_date: str, 
        dividend_type: str,
        method: str = "ratio"
    ) -> str:
        """生成缓存键
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            dividend_type: 复权类型
            method: 计算方法
            
        Returns:
            缓存键
        """
        key_str = f"{stock_code}_{start_date}_{end_date}_{dividend_type}_{method}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _get_disk_cache_path(self, cache_key: str) -> Path:
        """获取磁盘缓存文件路径
        
        Args:
            cache_key: 缓存键
            
        Returns:
            磁盘缓存文件路径
        """
        return self.disk_cache_dir / f"{cache_key}.pkl"
    
    def _is_cache_valid(self, cache_time: datetime) -> bool:
        """检查缓存是否有效
        
        Args:
            cache_time: 缓存时间
            
        Returns:
            缓存是否有效
        """
        return datetime.now() - cache_time < self.cache_ttl
    
    def get_cached_data(
        self, 
        stock_code: str, 
        start_date: str, 
        end_date: str, 
        dividend_type: str,
        method: str = "ratio"
    ) -> Optional[pd.DataFrame]:
        """获取缓存的复权价格数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            dividend_type: 复权类型
            method: 计算方法
            
        Returns:
            缓存的复权价格数据，如果不存在或过期返回None
        """
        try:
            cache_key = self._generate_cache_key(stock_code, start_date, end_date, dividend_type, method)
            
            # 首先检查内存缓存
            memory_data = self.memory_cache.get(cache_key)
            if memory_data is not None:
                data, cache_time = memory_data
                if self._is_cache_valid(cache_time):
                    self.stats['memory_hits'] += 1
                    logger.debug(f"内存缓存命中: {stock_code}")
                    return data
                else:
                    # 缓存过期，删除
                    self.memory_cache.remove(cache_key)
            
            self.stats['memory_misses'] += 1
            
            # 检查磁盘缓存
            disk_cache_path = self._get_disk_cache_path(cache_key)
            if disk_cache_path.exists():
                try:
                    with open(disk_cache_path, 'rb') as f:
                        data, cache_time = pickle.load(f)
                    
                    if self._is_cache_valid(cache_time):
                        # 磁盘缓存有效，加载到内存缓存
                        self.memory_cache.put(cache_key, (data, cache_time))
                        self.stats['disk_hits'] += 1
                        logger.debug(f"磁盘缓存命中: {stock_code}")
                        return data
                    else:
                        # 磁盘缓存过期，删除文件
                        disk_cache_path.unlink()
                        
                except Exception as e:
                    logger.warning(f"读取磁盘缓存失败: {e}")
                    # 删除损坏的缓存文件
                    if disk_cache_path.exists():
                        disk_cache_path.unlink()
            
            self.stats['disk_misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"获取缓存数据失败: {e}")
            return None
    
    def put_cached_data(
        self, 
        stock_code: str, 
        start_date: str, 
        end_date: str, 
        dividend_type: str,
        data: pd.DataFrame,
        method: str = "ratio"
    ) -> bool:
        """存储复权价格数据到缓存
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            dividend_type: 复权类型
            data: 复权价格数据
            method: 计算方法
            
        Returns:
            存储是否成功
        """
        try:
            cache_key = self._generate_cache_key(stock_code, start_date, end_date, dividend_type, method)
            cache_time = datetime.now()
            cache_data = (data, cache_time)
            
            # 存储到内存缓存
            self.memory_cache.put(cache_key, cache_data)
            
            # 存储到磁盘缓存
            disk_cache_path = self._get_disk_cache_path(cache_key)
            try:
                with open(disk_cache_path, 'wb') as f:
                    pickle.dump(cache_data, f)
            except Exception as e:
                logger.warning(f"存储磁盘缓存失败: {e}")
            
            self.stats['cache_puts'] += 1
            logger.debug(f"缓存数据存储成功: {stock_code}")
            return True
            
        except Exception as e:
            logger.error(f"存储缓存数据失败: {e}")
            return False
    
    def invalidate_cache(self, stock_code: str) -> int:
        """使指定股票的缓存失效
        
        Args:
            stock_code: 股票代码
            
        Returns:
            删除的缓存数量
        """
        try:
            removed_count = 0
            
            # 删除内存缓存中相关的数据
            keys_to_remove = []
            for key in self.memory_cache.keys():
                # 检查缓存键是否包含股票代码
                if stock_code in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self.memory_cache.remove(key)
                removed_count += 1
            
            # 删除磁盘缓存中相关的文件
            for cache_file in self.disk_cache_dir.glob("*.pkl"):
                try:
                    with open(cache_file, 'rb') as f:
                        data, cache_time = pickle.load(f)
                    
                    # 检查数据中是否包含指定股票
                    if hasattr(data, 'columns') and any(stock_code in str(col) for col in data.columns):
                        cache_file.unlink()
                        removed_count += 1
                except:
                    # 如果无法读取文件，跳过
                    continue
            
            logger.info(f"股票 {stock_code} 缓存失效完成，删除 {removed_count} 个缓存项")
            return removed_count
            
        except Exception as e:
            logger.error(f"使缓存失效失败: {e}")
            return 0
    
    def clear_expired_cache(self) -> int:
        """清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        try:
            removed_count = 0
            
            # 清理内存缓存中的过期数据
            keys_to_remove = []
            for key in self.memory_cache.keys():
                memory_data = self.memory_cache.get(key)
                if memory_data is not None:
                    data, cache_time = memory_data
                    if not self._is_cache_valid(cache_time):
                        keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self.memory_cache.remove(key)
                removed_count += 1
            
            # 清理磁盘缓存中的过期文件
            for cache_file in self.disk_cache_dir.glob("*.pkl"):
                try:
                    with open(cache_file, 'rb') as f:
                        data, cache_time = pickle.load(f)
                    
                    if not self._is_cache_valid(cache_time):
                        cache_file.unlink()
                        removed_count += 1
                except:
                    # 如果无法读取文件，删除它
                    cache_file.unlink()
                    removed_count += 1
            
            logger.info(f"清理过期缓存完成，删除 {removed_count} 个缓存项")
            return removed_count
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        try:
            # 计算命中率
            total_memory_requests = self.stats['memory_hits'] + self.stats['memory_misses']
            total_disk_requests = self.stats['disk_hits'] + self.stats['disk_misses']
            
            memory_hit_rate = (self.stats['memory_hits'] / total_memory_requests * 100) if total_memory_requests > 0 else 0
            disk_hit_rate = (self.stats['disk_hits'] / total_disk_requests * 100) if total_disk_requests > 0 else 0
            
            # 计算磁盘缓存大小
            disk_size_mb = 0
            if self.disk_cache_dir.exists():
                for cache_file in self.disk_cache_dir.glob("*.pkl"):
                    disk_size_mb += cache_file.stat().st_size
                disk_size_mb = disk_size_mb / (1024 * 1024)  # 转换为MB
            
            return {
                'memory_cache_size': self.memory_cache.size(),
                'memory_cache_capacity': self.memory_cache.capacity,
                'memory_hit_rate': round(memory_hit_rate, 2),
                'disk_cache_files': len(list(self.disk_cache_dir.glob("*.pkl"))),
                'disk_cache_size_mb': round(disk_size_mb, 2),
                'disk_hit_rate': round(disk_hit_rate, 2),
                'cache_ttl_hours': self.cache_ttl.total_seconds() / 3600,
                'stats': self.stats.copy()
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
            return {'error': str(e)}


# 创建全局实例
adjustment_price_cache = AdjustmentPriceCache()
