#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迅投xtquant数据索引格式测试

测试迅投API返回的DataFrame索引格式和类型
"""

import sys
import os
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)

# 导入迅投API
try:
    import xtquant.xtdata as xtdata
    XTQUANT_AVAILABLE = True
except ImportError:
    logger.warning("无法导入迅投API，将使用模拟数据进行测试")
    XTQUANT_AVAILABLE = False


def test_xtquant_index_format():
    """测试迅投API返回的数据索引格式"""
    
    if not XTQUANT_AVAILABLE:
        logger.warning("迅投API不可用，跳过实际API测试")
        return create_mock_xtquant_data()
    
    logger.info("=== 测试迅投xtquant数据索引格式 ===")
    
    try:
        # 测试获取本地数据
        logger.info("测试 get_local_data 返回的索引格式...")
        
        # 尝试获取一些常见股票的数据
        test_symbols = ["600000.SH", "000001.SZ"]
        
        for symbol in test_symbols:
            try:
                # 获取日线数据
                data_1d = xtdata.get_local_data(
                    field_list=[],
                    stock_list=[symbol],
                    period="1d",
                    start_time="20240101",
                    end_time="20240131"
                )
                
                if data_1d and symbol in data_1d:
                    df = data_1d[symbol]
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        analyze_dataframe_index(df, f"{symbol} 1d数据")
                
                # 获取分钟数据
                data_1m = xtdata.get_local_data(
                    field_list=[],
                    stock_list=[symbol],
                    period="1m",
                    start_time="20240101",
                    end_time="20240102"
                )
                
                if data_1m and symbol in data_1m:
                    df = data_1m[symbol]
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        analyze_dataframe_index(df, f"{symbol} 1m数据")
                
                # 获取tick数据
                data_tick = xtdata.get_local_data(
                    field_list=[],
                    stock_list=[symbol],
                    period="tick",
                    start_time="20240101",
                    end_time="20240101"
                )
                
                if data_tick and symbol in data_tick:
                    df = data_tick[symbol]
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        analyze_dataframe_index(df, f"{symbol} tick数据")
                        
            except Exception as e:
                logger.warning(f"获取 {symbol} 数据失败: {e}")
                continue
                
    except Exception as e:
        logger.error(f"测试迅投API时出错: {e}")
        return create_mock_xtquant_data()


def analyze_dataframe_index(df: pd.DataFrame, data_name: str):
    """分析DataFrame的索引格式"""
    
    logger.info(f"\n--- {data_name} 索引分析 ---")
    logger.info(f"数据形状: {df.shape}")
    logger.info(f"索引类型: {type(df.index)}")
    logger.info(f"索引数据类型: {df.index.dtype}")
    
    if len(df.index) > 0:
        logger.info(f"索引示例: {df.index[:5].tolist()}")
        
        # 检查索引是否为字符串
        if hasattr(df.index, 'str'):
            logger.info("索引支持字符串操作")
            if len(df.index) > 0:
                first_idx = df.index[0]
                logger.info(f"第一个索引值: {first_idx} (类型: {type(first_idx)})")
                
                if isinstance(first_idx, str):
                    logger.info(f"字符串长度: {len(first_idx)}")
                    if first_idx.isdigit():
                        logger.info("索引为纯数字字符串")
                    else:
                        logger.info("索引包含非数字字符")
        
        # 检查索引是否为数值类型
        if pd.api.types.is_numeric_dtype(df.index):
            logger.info("索引为数值类型")
            logger.info(f"数值范围: {df.index.min()} 到 {df.index.max()}")
        
        # 检查索引是否为时间类型
        if pd.api.types.is_datetime64_any_dtype(df.index):
            logger.info("索引为时间类型")
            logger.info(f"时间范围: {df.index.min()} 到 {df.index.max()}")
    
    # 检查是否有time列
    if 'time' in df.columns:
        logger.info("包含time列")
        time_col = df['time']
        logger.info(f"time列类型: {time_col.dtype}")
        if len(time_col) > 0:
            logger.info(f"time列示例: {time_col.iloc[:3].tolist()}")
            logger.info(f"time列值类型: {type(time_col.iloc[0])}")


def create_mock_xtquant_data():
    """创建模拟的迅投数据格式用于测试"""
    
    logger.info("=== 创建模拟迅投数据格式 ===")
    
    # 模拟日线数据 - 通常使用8位日期
    dates_1d = pd.date_range('2024-01-01', periods=20, freq='D')
    mock_1d = pd.DataFrame({
        'time': [int(d.strftime('%Y%m%d')) for d in dates_1d],
        'open': [100 + i for i in range(20)],
        'high': [105 + i for i in range(20)],
        'low': [95 + i for i in range(20)],
        'close': [102 + i for i in range(20)],
        'volume': [1000000 + i*10000 for i in range(20)]
    })
    # 设置索引为8位日期字符串
    mock_1d.index = [d.strftime('%Y%m%d') for d in dates_1d]
    analyze_dataframe_index(mock_1d, "模拟1d数据")
    
    # 模拟分钟数据 - 通常使用12位时间戳
    times_1m = pd.date_range('2024-01-01 09:30:00', periods=20, freq='1min')
    mock_1m = pd.DataFrame({
        'time': [int(t.strftime('%Y%m%d%H%M')) for t in times_1m],
        'open': [100 + i*0.1 for i in range(20)],
        'high': [100.5 + i*0.1 for i in range(20)],
        'low': [99.5 + i*0.1 for i in range(20)],
        'close': [100.2 + i*0.1 for i in range(20)],
        'volume': [10000 + i*100 for i in range(20)]
    })
    # 设置索引为12位时间字符串
    mock_1m.index = [t.strftime('%Y%m%d%H%M') for t in times_1m]
    analyze_dataframe_index(mock_1m, "模拟1m数据")
    
    # 模拟tick数据 - 通常使用14位时间戳
    times_tick = pd.date_range('2024-01-01 09:30:00', periods=20, freq='3s')
    mock_tick = pd.DataFrame({
        'time': [int(t.strftime('%Y%m%d%H%M%S')) for t in times_tick],
        'lastPrice': [100 + i*0.01 for i in range(20)],
        'volume': [100 + i*10 for i in range(20)],
        'amount': [10000 + i*1000 for i in range(20)],
        'bidPrice': [99.9 + i*0.01 for i in range(20)],
        'askPrice': [100.1 + i*0.01 for i in range(20)]
    })
    # 设置索引为14位时间字符串
    mock_tick.index = [t.strftime('%Y%m%d%H%M%S') for t in times_tick]
    analyze_dataframe_index(mock_tick, "模拟tick数据")
    
    return {
        '1d': mock_1d,
        '1m': mock_1m,
        'tick': mock_tick
    }


def compare_with_project_standard():
    """对比迅投格式与项目标准格式"""
    
    logger.info("\n=== 迅投格式 vs 项目标准格式对比 ===")
    
    # 项目标准：统一14位字符串索引
    logger.info("项目标准格式:")
    logger.info("- 所有周期统一使用14位字符串索引: YYYYMMDDHHMMSS")
    logger.info("- tick: 20240101093000 (精确到秒)")
    logger.info("- 1m:   20240101093000 (分钟对齐)")
    logger.info("- 1d:   20240101000000 (日期对齐)")
    
    logger.info("\n迅投原生格式推测:")
    logger.info("- tick: 可能使用14位数值或字符串")
    logger.info("- 1m:   可能使用12位数值或字符串")
    logger.info("- 1d:   可能使用8位数值或字符串")
    
    logger.info("\n格式转换的必要性:")
    logger.info("1. 统一性: 避免不同周期使用不同格式")
    logger.info("2. 可读性: 字符串格式更直观")
    logger.info("3. 兼容性: 统一接口处理")
    logger.info("4. 性能权衡: 牺牲部分查询性能换取一致性")


def research_industry_standards():
    """研究业界数据索引标准"""
    
    logger.info("\n=== 业界数据索引标准研究 ===")
    
    logger.info("主流量化平台索引格式:")
    logger.info("1. 聚宽(JoinQuant): DatetimeIndex")
    logger.info("2. 米筐(RiceQuant): DatetimeIndex")
    logger.info("3. 掘金(MyQuant): 数值时间戳")
    logger.info("4. Wind万得: 数值格式")
    logger.info("5. 同花顺iFinD: 混合格式")
    
    logger.info("\n数据库存储标准:")
    logger.info("1. InfluxDB: 纳秒时间戳")
    logger.info("2. TimescaleDB: timestamp类型")
    logger.info("3. ClickHouse: DateTime64类型")
    logger.info("4. MongoDB: ISODate类型")
    
    logger.info("\n文件存储标准:")
    logger.info("1. Parquet: timestamp类型")
    logger.info("2. HDF5: 数值时间戳")
    logger.info("3. CSV: 字符串格式")
    logger.info("4. Feather: DatetimeIndex")
    
    logger.info("\n性能优化考虑:")
    logger.info("1. 数值索引: 查询速度最快，内存占用最少")
    logger.info("2. DatetimeIndex: pandas原生支持，功能丰富")
    logger.info("3. 字符串索引: 可读性最好，但性能较差")
    logger.info("4. 混合方案: 根据场景选择不同格式")


if __name__ == "__main__":
    logger.info("开始迅投xtquant索引格式研究")
    
    # 测试迅投API返回的索引格式
    test_xtquant_index_format()
    
    # 对比项目标准
    compare_with_project_standard()
    
    # 研究业界标准
    research_industry_standards()
    
    logger.info("迅投xtquant索引格式研究完成")
