#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期货连续化智能缓存管理器

负责期货连续化数据的智能缓存管理，包括：
1. 基于使用频率的智能缓存失效机制
2. 7天未使用延时删除策略
3. Parquet格式缓存存储
4. 访问时间跟踪和统计

智能缓存策略：
- 访问时间跟踪：记录每次缓存访问的时间
- 延时删除：7天未使用才删除，而不是固定TTL
- 使用频率统计：提供缓存使用情况分析
- 自动清理：定期清理过期缓存
"""

import os
import hashlib
import pandas as pd
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import json

from config.settings import DATA_ROOT
from utils.logger.manager import get_unified_logger

logger = get_unified_logger(__name__)


class ContinuousCache:
    """期货连续化智能缓存管理器"""
    
    def __init__(self, cache_root: Optional[str] = None, unused_days_threshold: int = 7):
        """初始化连续化缓存管理器
        
        Args:
            cache_root: 缓存根目录，默认使用DATA_ROOT/cache/continuous
            unused_days_threshold: 未使用天数阈值，超过此天数的缓存将被删除
        """
        self.cache_root = Path(cache_root or os.path.join(DATA_ROOT, "cache", "continuous"))
        self.unused_days_threshold = unused_days_threshold
        self.access_tracker_file = self.cache_root / "access_tracker.json"
        
        # 确保缓存目录存在
        self.cache_root.mkdir(parents=True, exist_ok=True)
        
        # 加载访问跟踪数据
        self.access_tracker = self._load_access_tracker()
        
        logger.info(f"连续化缓存管理器初始化，缓存目录: {self.cache_root}, 未使用阈值: {unused_days_threshold}天")
    
    def _load_access_tracker(self) -> Dict[str, Dict[str, Any]]:
        """加载访问跟踪数据
        
        Returns:
            访问跟踪数据字典
        """
        try:
            if self.access_tracker_file.exists():
                with open(self.access_tracker_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换时间字符串为datetime对象
                    for cache_key, info in data.items():
                        if 'last_access' in info:
                            info['last_access'] = datetime.fromisoformat(info['last_access'])
                        if 'created_time' in info:
                            info['created_time'] = datetime.fromisoformat(info['created_time'])
                    return data
            else:
                return {}
        except Exception as e:
            logger.error(f"加载访问跟踪数据失败: {e}")
            return {}
    
    def _save_access_tracker(self):
        """保存访问跟踪数据"""
        try:
            # 转换datetime对象为字符串
            data_to_save = {}
            for cache_key, info in self.access_tracker.items():
                data_to_save[cache_key] = info.copy()
                if 'last_access' in data_to_save[cache_key]:
                    data_to_save[cache_key]['last_access'] = data_to_save[cache_key]['last_access'].isoformat()
                if 'created_time' in data_to_save[cache_key]:
                    data_to_save[cache_key]['created_time'] = data_to_save[cache_key]['created_time'].isoformat()
            
            with open(self.access_tracker_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存访问跟踪数据失败: {e}")
    
    def _generate_cache_key(self, symbol: str, continuous_type: str, method: str, **kwargs) -> str:
        """生成缓存键
        
        Args:
            symbol: 期货品种代码
            continuous_type: 连续化类型（main/weighted）
            method: 连续化方法（ratio/diff）
            **kwargs: 其他参数
            
        Returns:
            缓存键
        """
        # 构建缓存键字符串
        key_parts = [symbol, continuous_type, method]
        
        # 添加其他参数
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}={value}")
        
        key_string = "|".join(key_parts)
        
        # 生成MD5哈希
        cache_key = hashlib.md5(key_string.encode('utf-8')).hexdigest()
        
        logger.debug(f"生成缓存键: {key_string} -> {cache_key}")
        return cache_key
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径
        
        Args:
            cache_key: 缓存键
            
        Returns:
            缓存文件路径
        """
        return self.cache_root / f"{cache_key}.parquet"
    
    def get_cached_data(self, symbol: str, continuous_type: str, method: str, **kwargs) -> Optional[pd.DataFrame]:
        """获取缓存数据
        
        Args:
            symbol: 期货品种代码
            continuous_type: 连续化类型
            method: 连续化方法
            **kwargs: 其他参数
            
        Returns:
            缓存的数据，如果不存在则返回None
        """
        try:
            cache_key = self._generate_cache_key(symbol, continuous_type, method, **kwargs)
            cache_file = self._get_cache_file_path(cache_key)
            
            if not cache_file.exists():
                logger.debug(f"缓存文件不存在: {cache_key}")
                return None
            
            # 检查是否过期（基于使用频率）
            if self._is_cache_expired(cache_key):
                logger.info(f"缓存已过期，删除: {cache_key}")
                self._remove_cache(cache_key)
                return None
            
            # 读取缓存数据
            cached_data = pd.read_parquet(cache_file)
            
            # 更新访问记录
            self._update_access_record(cache_key)
            
            logger.debug(f"缓存命中: {cache_key}, 数据量: {len(cached_data)}")
            return cached_data
            
        except Exception as e:
            logger.error(f"获取缓存数据失败: {e}")
            return None
    
    def put_cached_data(self, data: pd.DataFrame, symbol: str, continuous_type: str, method: str, **kwargs) -> bool:
        """存储缓存数据
        
        Args:
            data: 要缓存的数据
            symbol: 期货品种代码
            continuous_type: 连续化类型
            method: 连续化方法
            **kwargs: 其他参数
            
        Returns:
            存储是否成功
        """
        try:
            cache_key = self._generate_cache_key(symbol, continuous_type, method, **kwargs)
            cache_file = self._get_cache_file_path(cache_key)
            
            # 保存数据为Parquet格式
            data.to_parquet(cache_file)
            
            # 记录缓存信息
            current_time = datetime.now()
            self.access_tracker[cache_key] = {
                'symbol': symbol,
                'continuous_type': continuous_type,
                'method': method,
                'created_time': current_time,
                'last_access': current_time,
                'access_count': 1,
                'data_size': len(data)
            }
            
            # 保存访问跟踪数据
            self._save_access_tracker()
            
            logger.debug(f"缓存数据已存储: {cache_key}, 数据量: {len(data)}")
            return True
            
        except Exception as e:
            logger.error(f"存储缓存数据失败: {e}")
            return False
    
    def _is_cache_expired(self, cache_key: str) -> bool:
        """检查缓存是否过期（基于使用频率）
        
        Args:
            cache_key: 缓存键
            
        Returns:
            是否过期
        """
        try:
            if cache_key not in self.access_tracker:
                return True
            
            last_access = self.access_tracker[cache_key]['last_access']
            days_unused = (datetime.now() - last_access).days
            
            # 7天未使用则认为过期
            return days_unused >= self.unused_days_threshold
            
        except Exception as e:
            logger.error(f"检查缓存过期状态失败: {e}")
            return True
    
    def _update_access_record(self, cache_key: str):
        """更新访问记录
        
        Args:
            cache_key: 缓存键
        """
        try:
            if cache_key in self.access_tracker:
                self.access_tracker[cache_key]['last_access'] = datetime.now()
                self.access_tracker[cache_key]['access_count'] += 1
                
                # 定期保存访问跟踪数据（每10次访问保存一次）
                if self.access_tracker[cache_key]['access_count'] % 10 == 0:
                    self._save_access_tracker()
                    
        except Exception as e:
            logger.error(f"更新访问记录失败: {e}")
    
    def _remove_cache(self, cache_key: str):
        """删除缓存
        
        Args:
            cache_key: 缓存键
        """
        try:
            cache_file = self._get_cache_file_path(cache_key)
            
            # 删除缓存文件
            if cache_file.exists():
                cache_file.unlink()
            
            # 删除访问记录
            if cache_key in self.access_tracker:
                del self.access_tracker[cache_key]
            
            logger.debug(f"缓存已删除: {cache_key}")
            
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        try:
            expired_keys = []
            
            for cache_key in list(self.access_tracker.keys()):
                if self._is_cache_expired(cache_key):
                    expired_keys.append(cache_key)
            
            # 删除过期缓存
            for cache_key in expired_keys:
                self._remove_cache(cache_key)
            
            # 保存更新后的访问跟踪数据
            if expired_keys:
                self._save_access_tracker()
            
            logger.info(f"清理过期缓存完成，清理数量: {len(expired_keys)}")
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
            return 0
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        try:
            total_caches = len(self.access_tracker)
            total_access_count = sum(info['access_count'] for info in self.access_tracker.values())
            
            # 计算缓存大小
            cache_size_mb = 0
            for cache_file in self.cache_root.glob("*.parquet"):
                cache_size_mb += cache_file.stat().st_size / (1024 * 1024)
            
            # 统计不同类型的缓存
            type_stats = {}
            for info in self.access_tracker.values():
                cache_type = f"{info['continuous_type']}_{info['method']}"
                if cache_type not in type_stats:
                    type_stats[cache_type] = 0
                type_stats[cache_type] += 1
            
            return {
                'total_caches': total_caches,
                'total_access_count': total_access_count,
                'cache_size_mb': round(cache_size_mb, 2),
                'type_statistics': type_stats,
                'unused_threshold_days': self.unused_days_threshold
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
            return {}
