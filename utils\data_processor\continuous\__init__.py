"""
期货连续化数据处理模块

该模块提供完整的期货连续化数据处理功能，包括：
1. 连续化因子存储管理（主力合约切换记录，永久存储）
2. 连续化计算引擎（基于主力合约切换算法）
3. 连续化价格缓存管理（派生数据，智能缓存）
4. 连续化数据合成器（统一接口）

架构设计：
- 基础数据层：原始期货合约数据 + 连续化因子数据（永久存储）
- 派生数据层：连续化价格数据 + 技术指标数据（智能缓存）
- 数据管理层：统一访问接口，自动选择存储或缓存策略

使用示例：
    # 推荐使用统一接口
    from utils.data_processor.continuous import continuous_synthesizer

    # 合成主力连续数据
    continuous_data = continuous_synthesizer.synthesize_continuous_data(
        symbol="rb00.SF",
        price_data=raw_data,
        continuous_type="main"
    )

    # 直接使用底层组件
    from utils.data_processor.continuous import continuous_factor_storage
    factors = continuous_factor_storage.query_continuous_factors("rb00.SF")
"""

from .continuous_factor_storage import ContinuousFactorStorage
from .continuous_engine import ContinuousEngine
from .continuous_cache import ContinuousCache
from .continuous_synthesizer import ContinuousSynthesizer

# 创建全局实例（使用智能缓存管理器）
from utils.cache_manager import continuous_cache_manager

continuous_factor_storage = ContinuousFactorStorage()
continuous_engine = ContinuousEngine()
continuous_cache = continuous_cache_manager  # 使用智能缓存管理器
continuous_synthesizer = ContinuousSynthesizer()

__all__ = [
    'ContinuousFactorStorage',
    'ContinuousEngine', 
    'ContinuousCache',
    'ContinuousSynthesizer',
    'continuous_factor_storage',
    'continuous_engine',
    'continuous_cache',
    'continuous_synthesizer'
]
