#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据操作模块

提供核心的数据操作API，包括下载、读取、列表、汇总、合成等功能
专注于数据处理的核心逻辑，不涉及用户交互
"""

import os
import sys
from typing import Dict, List, Optional, Union, Any

import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.data_helpers import DataHelpers, DisplayOptions, DateRange
from data.core.result_manager import get_result_manager, DownloadResult
from data.core.data_source_manager import get_manager
from data.storage.vectorized_reader import read_partitioned_data_vectorized

# 初始化
logger = get_unified_logger(__name__, enhanced=True)
data_manager = get_manager()
helpers = DataHelpers()


def download_data(
    stocks: Union[str, List[str]],
    period: str = "1d",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    incremental: bool = True,
    dividend_type: str = "none",  # 修改：默认存储原始数据，不进行复权处理
    data_dir: Optional[str] = None,
    show_data: bool = True,
    real_time_log: bool = True,
    real_time_save: bool = True,
    result_file: Optional[str] = None,
    display_rows: int = 5,
    display_head_rows: Optional[int] = None,
    display_tail_rows: Optional[int] = None,
    handle_custom_period: bool = True,
    process_time: bool = False,
    set_time_index: bool = True,
) -> bool:
    """
    下载股票数据

    ⚠️ 重要说明：系统会自动从 download_results.txt 文件中读取未下载股票列表进行下载

    Args:
        stocks: 股票代码或股票代码列表（⚠️ 已废弃：此参数仅保留兼容性，实际股票列表从download_results.txt文件读取）
        period: 数据周期
        start_date: 开始日期
        end_date: 结束日期
        incremental: 是否增量更新
        dividend_type: 除权类型，默认"none"（原始数据），可选"front"（前复权）、"back"（后复权）
        data_dir: 数据目录
        show_data: 是否显示数据
        real_time_log: 是否实时日志
        real_time_save: 是否实时保存
        result_file: 结果文件路径（保留兼容性）
        display_rows: 显示行数
        display_head_rows: 头部显示行数
        display_tail_rows: 尾部显示行数
        handle_custom_period: 是否处理自定义周期
        process_time: 是否处理时间戳
        set_time_index: 是否设置时间索引

    Returns:
        下载是否成功

    Note:
        实际的股票列表会从 download_results.txt 文件中的未下载股票列表读取，
        stocks 参数已废弃但保留以维持API兼容性。

        ⚠️ 存储策略变更：
        - 默认存储原始数据（dividend_type="none"），不进行复权处理
        - 前复权数据将在回测时动态合成，无需专门存储
        - 这样可以避免除权事件后需要重新下载所有历史数据的问题
    """
    logger.debug(LogTarget.FILE, "开始执行download_data函数")
    
    try:
        # 使用结果管理器获取未下载股票，支持自定义结果文件和周期参数
        result_manager = get_result_manager(data_dir, result_file)
        stock_list = result_manager.get_undownloaded_stocks_from_file(period=period)

        if not stock_list:
            logger.error(LogTarget.FILE, f"结果文件中没有未下载的股票，请检查结果文件: {result_manager.result_file}")
            return False

        # 处理日期参数
        date_range = helpers.process_date_params(period, start_date, end_date)
        
        # 处理显示选项
        display_options = helpers.parse_display_options(display_rows, display_head_rows, display_tail_rows)

        # 执行下载（现在每只股票处理完都会立即保存结果）
        result = _execute_sequential_download(
            stock_list=stock_list,
            period=period,
            start_date=date_range.start_date,
            end_date=date_range.end_date,
            incremental=incremental,
            dividend_type=dividend_type,
            data_dir=data_dir,
            real_time_log=real_time_log,
            real_time_save=real_time_save,
            display_options=display_options,
            handle_custom_period=handle_custom_period,
            show_data=show_data,
            process_time=process_time,
            set_time_index=set_time_index,
            result_file=result_file
        )

        # 注意：下载结果已在处理过程中实时保存，无需再次保存
        logger.info(LogTarget.FILE, "所有股票处理完成，结果已实时保存到文件")

        # 记录结果
        result_manager.log_download_results(result)

        return result.success

    except Exception as e:
        logger.error(LogTarget.FILE, f"下载数据时发生异常: {e}", exc_info=True)
        return False
    finally:
        logger.debug(LogTarget.FILE, "download_data函数执行完毕\n"+"="*60+"\n"*3)


# read_data函数已删除 - DRY原则清理：重复接口，统一使用data.processing.load_data()
# 请使用: from data.processing import load_data

def read_data_DEPRECATED_USE_PROCESSING_LOAD_DATA_INSTEAD(
    data_root: str = None,
    symbol: str = None,
    period: str = "1d",
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    head_lines: Optional[int] = None,
    tail_lines: Optional[int] = None,
    columns: Optional[List[str]] = None,
    display_mode: str = "both",
    set_time_index: bool = True,
    data_type: str = "raw",  # 数据类型：raw（原始数据）或adjusted（复权数据）
    adj_type: Optional[str] = None,  # 复权类型：front（前复权）或back（后复权）
    # 兼容旧参数
    symbols: Optional[List[str]] = None,
    fields: Optional[List[str]] = None,
    mode: Optional[str] = None,
    lines: Optional[int] = None,
    quiet_console: bool = False,
) -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
    """
    读取数据
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        head_lines: 头部行数
        tail_lines: 尾部行数
        columns: 列名列表
        display_mode: 显示模式
        set_time_index: 是否设置时间索引
        dividend_type: 复权类型，"none"（原始数据）、"front"（前复权）、"back"（后复权）

        # 兼容参数
        symbols: 股票代码列表（兼容）
        fields: 字段列表（兼容）
        mode: 显示模式（兼容）
        lines: 行数（兼容）
        quiet_console: 是否静默
        
    Returns:
        数据框或数据框字典
    """
    # 处理兼容性参数
    is_old_style = symbols is not None
    return_dict = is_old_style
    
    # 设置数据根目录
    if data_root is None:
        data_root = DATA_ROOT
    
    # 处理股票代码
    if symbols is not None:
        if isinstance(symbols, str):
            symbols = [symbols]
        if not symbols:
            logger.error(LogTarget.FILE, "未提供有效的股票代码")
            return {} if return_dict else None
    else:
        if symbol is None:
            logger.error(LogTarget.FILE, "未提供股票代码")
            return {} if return_dict else None
        symbols = [symbol]
    
    # 处理其他兼容参数
    if fields is not None:
        columns = fields
    if mode is not None:
        display_mode = mode
    if lines is not None:
        if display_mode == "head":
            head_lines = lines
            tail_lines = 0
        elif display_mode == "tail":
            head_lines = 0
            tail_lines = lines
        elif display_mode == "both":
            head_lines = lines
            tail_lines = lines
    
    # 读取数据
    if is_old_style:
        data_dict = {}
        for stock in symbols:
            try:
                df = _read_single_stock_data(
                    data_root=data_root,
                    symbol=stock,
                    period=period,
                    start_time=start_time,
                    end_time=end_time,
                    head_lines=head_lines,
                    tail_lines=tail_lines,
                    columns=columns,
                    display_mode=display_mode,
                    set_time_index=set_time_index
                )
                data_dict[stock] = df if df is not None else pd.DataFrame()
                
                # 如果不是静默模式，显示数据
                if not quiet_console and df is not None and not df.empty:
                    rows = len(df)
                    cols = len(df.columns)
                    logger.info(LogTarget.FILE, f"{stock} - {period} (数据行数: {rows}, 列数: {cols}):")
                    
                    # 使用格式化函数显示数据
                    from data.core.data_source_manager import pd_format
                    pf_data = pd_format(
                        df=df,
                        data=display_mode,
                        head_rows=head_lines,
                        tail_rows=tail_lines
                    )
                    logger.debug(LogTarget.FILE, f"数据为:\n{pf_data}")
            except Exception as e:
                logger.error(LogTarget.FILE, f"{stock}: 读取数据失败: {e}")
                data_dict[stock] = pd.DataFrame()
        
        return data_dict
    else:
        # 使用新风格参数，只读取一个股票
        return _read_single_stock_data(
            data_root=data_root,
            symbol=symbols[0],
            period=period,
            start_time=start_time,
            end_time=end_time,
            head_lines=head_lines,
            tail_lines=tail_lines,
            columns=columns,
            display_mode=display_mode,
            set_time_index=set_time_index
        )


def list_data(
    data_dir: Optional[str] = None,
    market: Optional[str] = None,
    stock: Optional[str] = None,
    period: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    列出本地可用的数据文件
    
    Args:
        data_dir: 数据目录
        market: 市场代码
        stock: 股票代码
        period: 数据周期
        
    Returns:
        数据文件信息列表
    """
    root_dir = data_dir if data_dir else DATA_ROOT
    files_info = []

    if not os.path.exists(root_dir):
        logger.error(f"数据目录 {root_dir} 不存在")
        return files_info

    try:
        # 遍历数据目录
        for market_dir in os.listdir(root_dir):
            market_path = os.path.join(root_dir, market_dir)
            if not os.path.isdir(market_path):
                continue
            if market and market.upper() != market_dir.upper():
                continue

            for stock_dir in os.listdir(market_path):
                stock_path = os.path.join(market_path, stock_dir)
                if not os.path.isdir(stock_path):
                    continue
                if stock and stock != stock_dir:
                    continue

                for file in os.listdir(stock_path):
                    file_path = os.path.join(stock_path, file)
                    if not os.path.isfile(file_path):
                        continue

                    filename, ext = os.path.splitext(file)
                    if ext.lower() != ".parquet":
                        continue
                    if period and period != filename:
                        continue

                    # 获取文件信息
                    file_stats = os.stat(file_path)
                    from datetime import datetime
                    last_mod_time = datetime.fromtimestamp(file_stats.st_mtime)
                    last_mod_str = last_mod_time.strftime("%Y-%m-%d %H:%M:%S")

                    file_info = {
                        "market": market_dir,
                        "stock": stock_dir,
                        "period": filename,
                        "path": file_path,
                        "size": file_stats.st_size,
                        "last_modified": last_mod_str,
                        "full_symbol": f"{stock_dir}.{market_dir}",
                    }
                    files_info.append(file_info)

        # 排序并显示
        files_info.sort(key=lambda x: (x["market"], x["stock"], x["period"]))

        if files_info:
            data = []
            for info in files_info:
                # 格式化文件大小
                size = info["size"]
                if size < 1024:
                    size_str = f"{size}B"
                elif size < 1024 * 1024:
                    size_str = f"{size/1024:.1f}KB"
                else:
                    size_str = f"{size/(1024*1024):.1f}MB"

                data.append({
                    "市场": info["market"],
                    "股票": info["stock"],
                    "周期": info["period"],
                    "大小": size_str,
                    "修改时间": info["last_modified"],
                    "完整代码": info["full_symbol"],
                })

            df = pd.DataFrame(data)
            from data.core.data_source_manager import pd_format
            pf_data = pd_format(df=df, data="all")
            logger.debug(LogTarget.FILE, f"可用数据文件:\n{pf_data}")
        else:
            logger.info("未找到符合条件的数据文件")

        return files_info

    except Exception as e:
        logger.exception(f"列出数据文件时出错: {str(e)}")
        return []


def summarize_data(data_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    汇总数据目录中的可用数据
    
    Args:
        data_dir: 数据目录
        
    Returns:
        数据统计信息
    """
    root_dir = data_dir if data_dir else DATA_ROOT

    if not os.path.exists(root_dir):
        logger.error(f"数据目录 {root_dir} 不存在")
        return {}

    summary = {
        "total_markets": 0,
        "total_stocks": 0,
        "total_files": 0,
        "total_size": 0,
        "markets": {},
        "periods": {},
        "last_updated": None,
    }

    try:
        from datetime import datetime
        
        # 遍历数据目录
        for market_dir in os.listdir(root_dir):
            market_path = os.path.join(root_dir, market_dir)
            if not os.path.isdir(market_path):
                continue

            market_stats = {
                "total_stocks": 0,
                "total_files": 0,
                "total_size": 0,
                "periods": {},
            }

            for stock_dir in os.listdir(market_path):
                stock_path = os.path.join(market_path, stock_dir)
                if not os.path.isdir(stock_path):
                    continue

                stock_stats = {"files": 0, "size": 0, "periods": []}

                for file in os.listdir(stock_path):
                    file_path = os.path.join(stock_path, file)
                    if not os.path.isfile(file_path):
                        continue

                    filename, ext = os.path.splitext(file)
                    if ext.lower() != ".parquet":
                        continue

                    file_stats = os.stat(file_path)
                    stock_stats["files"] += 1
                    stock_stats["size"] += file_stats.st_size
                    stock_stats["periods"].append(filename)

                    market_stats["total_files"] += 1
                    market_stats["total_size"] += file_stats.st_size

                    if filename not in market_stats["periods"]:
                        market_stats["periods"][filename] = 0
                    market_stats["periods"][filename] += 1

                    if filename not in summary["periods"]:
                        summary["periods"][filename] = 0
                    summary["periods"][filename] += 1

                    last_mod = datetime.fromtimestamp(file_stats.st_mtime)
                    if not summary["last_updated"] or last_mod > summary["last_updated"]:
                        summary["last_updated"] = last_mod

                if stock_stats["files"] > 0:
                    market_stats["total_stocks"] += 1
                    summary["total_stocks"] += 1
                    summary["total_files"] += stock_stats["files"]
                    summary["total_size"] += stock_stats["size"]

            if market_stats["total_stocks"] > 0:
                summary["markets"][market_dir] = market_stats
                summary["total_markets"] += 1

        # 格式化最后更新时间
        if summary["last_updated"]:
            dt_format = "%Y-%m-%d %H:%M:%S"
            last_update_str = summary["last_updated"].strftime(dt_format)
            summary["last_updated"] = last_update_str

        # 打印摘要信息
        logger.info("\n数据统计摘要")
        logger.info(f"数据根目录: {root_dir}")
        logger.info(f"市场总数: {summary['total_markets']}")
        logger.info(f"股票总数: {summary['total_stocks']}")
        logger.info(f"文件总数: {summary['total_files']}")

        # 格式化总大小
        total_size = summary["total_size"]
        if total_size < 1024:
            size_str = f"{total_size}B"
        elif total_size < 1024 * 1024:
            size_str = f"{total_size/1024:.1f}KB"
        elif total_size < 1024 * 1024 * 1024:
            size_str = f"{total_size/(1024*1024):.1f}MB"
        else:
            size_str = f"{total_size/(1024*1024*1024):.1f}GB"

        logger.info(f"数据总大小: {size_str}")
        logger.info(f"最后更新时间: {summary['last_updated']}")

        return summary

    except Exception as e:
        logger.exception(f"汇总数据时出错: {str(e)}")
        return {}


def synthesize_data(
    symbols: Union[str, List[str]],
    source_period: str = "1m",
    target_period: str = "3m",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    data_dir: Optional[str] = None,
    show_data: bool = True,
    display_rows: int = 5,
    result_file: Optional[str] = None,
    real_time_save: bool = True,
    dividend_type: str = "none",  # 新增：复权类型，默认为原始数据
) -> Dict[str, Any]:
    """
    从本地数据合成新周期数据（支持复权数据）

    Args:
        symbols: 股票代码
        source_period: 源数据周期
        target_period: 目标周期
        start_date: 开始日期
        end_date: 结束日期
        data_dir: 数据目录
        show_data: 是否显示数据
        display_rows: 显示行数
        result_file: 结果文件路径，用于即时保存
        real_time_save: 是否启用即时保存，默认True
        dividend_type: 复权类型，"none"（原始数据）、"front"（前复权）、"back"（后复权）

    Returns:
        合成结果字典
    """
    from utils.data_processor.period_handler import synthesize_from_local_data
    
    # 标准化股票代码
    if isinstance(symbols, str):
        stock_list = [symbols.strip()]
    else:
        stock_list = [code.strip() for code in symbols]
    
    stock_list = helpers.standardize_stock_codes(stock_list)
    
    # 处理日期
    date_range = helpers.process_date_params(source_period, start_date, end_date)
    
    # 打印操作信息
    stock_str = helpers.format_stock_list_display(stock_list)
    logger.info(
        LogTarget.FILE,
        f"周期合成参数：股票代码={stock_str}, 源周期={source_period}, 目标周期={target_period}, "
        f"开始日期={date_range.start_date}, 结束日期={date_range.end_date}"
    )

    # 合成数据
    result = synthesize_from_local_data(
        symbols=stock_list,
        source_period=source_period,
        target_period=target_period,
        start_time=date_range.start_date,
        end_time=date_range.end_date,
        data_dir=data_dir,
        result_file=result_file,
        real_time_save=real_time_save,
        dividend_type=dividend_type  # 传递复权类型参数
    )

    if result["success"]:
        logger.info(LogTarget.FILE, "数据合成成功")

        # 显示结果
        if show_data:
            successful_symbols = result.get("successful_symbols", [])
            failed_symbols = result.get("failed_symbols", [])

            if successful_symbols:
                logger.info(LogTarget.FILE, "成功合成的股票:")
                for symbol in successful_symbols:
                    if "data" in result and symbol in result["data"]:
                        df = result["data"][symbol]
                        rows = len(df) if df is not None and not df.empty else 0
                        logger.info(LogTarget.FILE, f"  - {symbol}: {rows} 行 {target_period} 数据")
                        logger.debug(LogTarget.FILE, f"{symbol} 合成成功数据示例:\n{df}")

            if failed_symbols:
                logger.info(LogTarget.FILE, "\n合成失败的股票:")
                for symbol in failed_symbols:
                    reason = result.get("failed_reasons", {}).get(symbol, "未知原因")
                    logger.info(LogTarget.FILE, f"  - {symbol}: {reason}")

            # 总结
            total = len(successful_symbols) + len(failed_symbols)
            if total > 0:
                success_rate = len(successful_symbols) / total * 100
                logger.info(
                    LogTarget.FILE,
                    f"\n合成汇总: 成功 {len(successful_symbols)}/{total} ({success_rate:.1f}%)"
                )

    else:
        logger.error(LogTarget.FILE, "数据合成失败")

    return result


def standardize_stock_codes(stock_codes: List[str]) -> List[str]:
    """
    标准化股票代码
    
    Args:
        stock_codes: 股票代码列表
        
    Returns:
        标准化后的股票代码列表
    """
    return helpers.standardize_stock_codes(stock_codes)


# 内部辅助函数
def _execute_sequential_download(stock_list: List[str], **kwargs) -> DownloadResult:
    """按顺序执行股票下载"""
    result = DownloadResult()

    # 处理data_dir参数
    data_dir = kwargs.get('data_dir')
    if data_dir:
        data_manager.data_root = data_dir
    else:
        data_dir = DATA_ROOT

    # 使用传递的result_file参数，如果没有则使用默认文件
    result_file = kwargs.get('result_file')
    if not result_file:
        result_file = os.path.join(data_dir, "download_results.txt")
    kwargs['result_file'] = result_file

    # 初始化结果管理器用于实时保存，使用指定的结果文件
    from data.core.result_manager import get_result_manager
    result_manager = get_result_manager(data_dir, result_file)

    total_stocks = len(stock_list)
    logger.info(LogTarget.FILE, f"开始处理 {total_stocks} 个股票")

    for i, symbol in enumerate(stock_list):
        logger.info(LogTarget.FILE, f"[{i+1}/{total_stocks}] 处理: {symbol}")

        try:
            # 调用数据源管理器下载单个股票
            symbol_result = data_manager.download_history_data(
                stock_list=[symbol],
                period=kwargs.get('period'),
                start_time=kwargs.get('start_date'),
                end_time=kwargs.get('end_date'),
                dividend_type=kwargs.get('dividend_type', 'front'),
                incremental=kwargs.get('incremental', True),
                real_time_log=kwargs.get('real_time_log', True),
                real_time_save=kwargs.get('real_time_save', True),
                result_file=result_file,
                display_rows=kwargs.get('display_rows', 5),
                display_head_rows=kwargs.get('display_options').head_lines,
                display_tail_rows=kwargs.get('display_options').tail_lines,
                handle_custom_period=kwargs.get('handle_custom_period', True),
                show_data=kwargs.get('show_data', True),
                process_time=kwargs.get('process_time', False),
                set_time_index=kwargs.get('set_time_index', True),
                close_pool=(i == total_stocks - 1),
            )

            # 转换为DownloadResult格式并合并
            symbol_download_result = _convert_to_download_result(symbol_result)
            result = _merge_download_results_objects(result, symbol_download_result)

            # 立即保存当前股票的处理结果
            try:
                # 计算当前未下载的股票列表（剩余的股票）
                remaining_stocks = stock_list[i+1:]  # 当前股票之后的股票

                result_manager.save_download_results(
                    successful_symbols=result.successful_symbols or [],
                    failed_symbols=result.failed_symbols or [],
                    not_downloaded_symbols=remaining_stocks,  # 剩余未处理的股票
                    period=kwargs.get('period'),
                    start_time=kwargs.get('start_date'),
                    end_time=kwargs.get('end_date'),
                    no_download_needed_symbols=result.no_download_needed or []
                )
                logger.debug(LogTarget.FILE, f"已保存 {symbol} 的处理结果到文件")
            except Exception as save_e:
                logger.error(LogTarget.FILE, f"保存 {symbol} 处理结果失败: {save_e}", exc_info=True)

        except Exception as e:
            logger.error(LogTarget.FILE, f"{symbol} 处理失败: {e}", exc_info=True)
            result.success = False
            if symbol not in result.failed_symbols:
                result.failed_symbols.append(symbol)
            result.failed_reasons[symbol] = str(e)

            # 即使处理失败也要保存结果
            try:
                remaining_stocks = stock_list[i+1:]
                result_manager.save_download_results(
                    successful_symbols=result.successful_symbols or [],
                    failed_symbols=result.failed_symbols or [],
                    not_downloaded_symbols=remaining_stocks,
                    period=kwargs.get('period'),
                    start_time=kwargs.get('start_date'),
                    end_time=kwargs.get('end_date'),
                    no_download_needed_symbols=result.no_download_needed or []
                )
                logger.debug(LogTarget.FILE, f"已保存 {symbol} 的失败结果到文件")
            except Exception as save_e:
                logger.error(LogTarget.FILE, f"保存 {symbol} 失败结果时出错: {save_e}", exc_info=True)

    # 计算最终未下载的股票列表（原始列表减去已处理的股票）
    processed_symbols = set(result.successful_symbols + result.failed_symbols + result.no_download_needed)
    result.not_downloaded_symbols = [s for s in stock_list if s not in processed_symbols]

    return result


def _convert_to_download_result(result_dict: Dict[str, Any]) -> DownloadResult:
    """将字典格式的结果转换为DownloadResult对象"""
    return DownloadResult(
        success=result_dict.get("success", False),
        successful_symbols=result_dict.get("successful_symbols", []),
        failed_symbols=result_dict.get("failed_symbols", []),
        not_downloaded_symbols=result_dict.get("not_downloaded_symbols", []),
        no_download_needed=result_dict.get("no_download_needed", []),
        data=result_dict.get("data", {}),
        save_paths=result_dict.get("save_paths", {}),
        logged_data_details=result_dict.get("logged_data_details", {}),
        failed_reasons=result_dict.get("failed_reasons", {})
    )


def _merge_download_results_objects(result1: DownloadResult, result2: DownloadResult) -> DownloadResult:
    """合并两个DownloadResult对象"""
    result_manager = get_result_manager()
    return result_manager.merge_results(result1, result2)


def _read_single_stock_data(
    data_root: str,
    symbol: str,
    period: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    head_lines: Optional[int] = None,
    tail_lines: Optional[int] = None,
    columns: Optional[List[str]] = None,
    display_mode: str = "both",
    set_time_index: bool = True,
) -> Optional[pd.DataFrame]:
    """读取单个股票的数据"""
    # 根据显示模式选择合适的数据读取函数
    if display_mode in ["head", "tail", "both"] and (head_lines is not None or tail_lines is not None):
        logger.debug(
            LogTarget.FILE,
            "使用高效读取函数 read_head_tail_data，显示模式: {}".format(display_mode)
        )

        # 设置默认值
        h_lines = head_lines if head_lines is not None else 0
        t_lines = tail_lines if tail_lines is not None else 0

        # 导入高效读取函数
        from data.storage.parquet_reader import read_head_tail_data

        # 使用高效读取函数
        df = read_head_tail_data(
            data_root=data_root,
            symbol=symbol,
            period=period,
            head_lines=h_lines,
            tail_lines=t_lines,
            columns=columns,
            start_time=start_time,
            end_time=end_time,
            mode=display_mode
        )

        return df
    else:
        # 对于显示全部数据的情况，使用向量化读取函数
        logger.debug(
            LogTarget.FILE,
            "使用向量化读取函数 read_partitioned_data_vectorized，显示模式: {}".format(display_mode)
        )

        # 使用向量化读取获得高性能
        from data.storage.vectorized_reader import read_partitioned_data_vectorized

        df = read_partitioned_data_vectorized(
            data_root=data_root,
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            columns=columns
        )

        # 根据显示模式筛选数据
        if df is not None and not df.empty:
            if head_lines and tail_lines:
                # 同时显示头部和尾部
                df_head = df.head(head_lines)
                df_tail = df.tail(tail_lines)
                df = pd.concat([df_head, df_tail])
                df = df[~df.index.duplicated(keep='first')]
                if isinstance(df.index, pd.DatetimeIndex):
                    df = df.sort_index()
            elif head_lines:
                # 只显示头部
                df = df.head(head_lines)
            elif tail_lines:
                # 只显示尾部
                df = df.tail(tail_lines)

        return df

    # 删除try-catch掩盖 - 遵循核心指导思维：宁可报错也不掩盖bug
    # 如果出现错误，直接抛出，不使用后备方案掩盖问题