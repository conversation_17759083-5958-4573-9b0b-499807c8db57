# 数据存储模块（统一路径管理版）

## 简介

数据存储模块负责将数据保存到磁盘并从磁盘加载数据。当前实现使用Parquet格式存储数据，支持按日期分区存储。

## 🔧 最新更新

### v2.5.0 - 2025-08-01 (全面优化升级)
- **🚀 向量化读取器智能文件选择**: 集成智能文件选择到VectorizedDataReader，实现90%+性能提升
- **🔄 统一时间过滤函数**: 消除重复实现，将filter_data_by_time_range统一到utils.time_utils模块
- **⚡ 头尾数据读取优化**: 智能选择最少必要文件，避免读取中间不必要文件
- **🧹 代码清理**: 删除包装函数，直接使用path_manager模块，简化代码结构
- **📊 性能验证**: 综合测试显示99.4%性能提升（24秒→0.14秒），3/4项测试通过

### v2.4.0 - 2025-08-01 (智能文件选择)
- **🚀 智能文件选择**: 新增`_get_target_partition_files`函数，利用`build_partitioned_path`直接构建目标文件路径
- **⚡ 性能大幅提升**: 从读取1354个文件优化到精确选择2个文件，数据读取时间从24秒优化到0.24秒
- **🗑️ 删除低效逻辑**: 移除`_filter_partition_files_by_time_range`垃圾过滤函数
- **🎯 精确文件定位**: 基于时间范围直接生成目标文件路径，避免扫描所有文件
- **✅ 功能完整性**: 保持所有原有功能正常工作，确保向后兼容

### v2.3.0 - 2025-08-01
- **DRY原则重构**: 消除重复模块，将enhanced_parquet_reader.py功能集成到parquet_reader.py中
- **循环依赖修复**: 彻底解决parquet_reader和enhanced_parquet_reader之间的循环依赖问题
- **增强功能集成**: EnhancedParquetReader类现已集成到主模块中，提供类型安全和性能监控
- **向后兼容性**: 保持所有现有API不变，确保平滑升级
- **代码简化**: 删除重复代码，统一实现，减少维护成本

### v2.2.0 - 2025-07-31
- **重构系统集成修复**: 修复parquet_reader.py中create_context方法调用错误
- **错误处理机制优化**: 正确使用error_handler.create_context替代不存在的DataTypeManager.create_context
- **数据读取功能恢复**: 解决"600000.SH 指定时间范围内tick数据为空"问题
- **系统稳定性提升**: 消除'DataTypeManager' object has no attribute 'create_context'错误
- **测试验证完成**: 验证600000.SH tick数据正常读取6,839,856行数据

### v2.1.0 - 2025-07-30
- **数据格式一致性修复**: 解决time列混合格式问题，恢复数值格式存储
- **读取时格式统一**: 在分区数据合并前统一time列格式，确保数据一致性
- **PyArrow兼容性优化**: 完善混合格式处理和错误恢复机制
- **全面测试验证**: 5/5测试通过，包括格式一致性、时间范围获取、PyArrow存储、混合格式处理和大数据集性能

### v2.0.0 - 2025-07-30
**v2.0 更新**：集成统一路径管理器（PathManager），解决路径不一致问题，提升系统稳定性和可靠性。

## 主要功能

1. **按分区保存数据**：
   - Tick数据按日期分区：`<data_root>/<市场>/<代码>/tick/<年>/<月>/<日>.parquet`
   - 其他周期按年份分区：`<data_root>/<市场>/<代码>/<周期>/<年>.parquet`

2. **并行处理**：
   - 支持多线程和多进程并行处理
   - 用户可通过配置文件控制线程数和处理模式

3. **增量更新**：
   - 支持将新数据追加到现有分区
   - 自动处理重复数据

4. **元数据管理**：
   - 存储和读取数据的元数据
   - 包括创建时间、行数等信息

5. **向量化数据读取**：
   - 基于Pandas Concat的高性能向量化读取
   - 完全保持数据原始结构和索引
   - 支持多线程并行读取优化
   - 智能缓存和内存优化
   - **统一索引格式处理和保护**

6. **统一索引格式标准**：
   - 标准格式：YYYYMMDDHHMMSS (14位时间戳)
   - 支持字符串索引和DatetimeIndex
   - 禁止数字索引 (0,1,2,3...)
   - 自动索引格式验证和修复

## 优化特性

最近的优化包括：

1. **时间范围文件过滤优化（2025-08-01新增）**：
   - **智能分区文件选择**：根据时间范围自动过滤不相关的分区文件
   - **文件级别过滤**：在文件读取前就排除不需要的分区，而不是读取后过滤
   - **性能提升显著**：对于小时间范围查询，性能提升可达98%以上
   - **优化效果**：从读取1354个文件（680万行，24秒）优化到13个文件（6.5万行，0.34秒）

2. **优化数据转换和分组**：
   - 使用临时Series而不是添加新列到DataFrame
   - 直接获取年份并分组，减少内存使用和计算开销

2. **优化并行处理**：
   - 任务按数据量平衡分配
   - 保持用户对线程数的完全控制

3. **优化日志记录**：
   - 减少DEBUG级别的日志，只保留关键INFO级别日志
   - 减少I/O开销

4. **优化文件操作**：
   - 统一处理目录创建，避免重复操作
   - 参数化文件操作

5. **优化PyArrow表创建**：
   - 根据数据特性选择最优的转换方法
   - 对于主要包含数值型列的DataFrame，使用更高效的方式创建表

6. **数据扫描日志优化（v2.0）**：
   - **find_available_data函数优化**: 将277条单独日志减少到5条汇总信息，减少98.2%
   - **summarize_data_directory函数优化**: 同样实现批量日志控制
   - **智能进度跟踪**: 每50个月份和每5个年份输出进度日志
   - **完整汇总信息**: 包含市场数、代码数、周期数、年份数、月份数、文件数、耗时等关键指标
   - **性能提升**: 大幅减少日志文件大小，提升系统响应速度

## 使用方法

### 数据存储

```python
from data.storage.parquet_storage import save_data_by_partition

# 保存数据
save_data_by_partition(
    df,              # DataFrame
    "D:/data",       # 数据根目录
    "000001.SZ",     # 股票代码
    "1d",            # 数据周期
    parallel=True,   # 是否使用并行处理
    use_threading=True,  # 是否使用多线程（False则使用多进程）
    max_workers=4    # 最大工作线程/进程数
)
```

### 向量化数据读取（推荐使用，已修复时间过滤功能）

```python
# 推荐方式1：直接使用向量化读取（已修复时间过滤）
from data.storage.vectorized_reader import read_partitioned_data_vectorized

df = read_partitioned_data_vectorized(
    data_root="D:/data",     # 数据根目录
    symbol="000001.SZ",      # 股票代码
    period="1d",             # 数据周期
    start_time="20240101",   # 开始时间（现已正确过滤）
    end_time="20241231",     # 结束时间（现已正确过滤）
    columns=["open", "high", "low", "close"]  # 指定列（可选）
)

# 推荐方式2：使用统一接口
from data.processing import load_data

df = load_data(
    data_root="D:/data",
    symbol="000001.SZ",
    period="1d",
    start_time="20240101",
    end_time="20241231"
)

# 使用向量化读取器（高级用法）
from data.storage.vectorized_reader import VectorizedDataReader

reader = VectorizedDataReader(enable_cache=True, cache_size=100)
df = reader.read_partitioned_data_vectorized(
    data_root="D:/data",
    symbol="000001.SZ",
    period="1d"
)

# 获取性能统计
stats = reader.get_performance_stats()
print(f"缓存命中率: {stats['cache_hit_rate']:.2%}")
print(f"平均处理时间: {stats['avg_time_per_call']:.6f}秒")
```

### 增强型数据读取（类型安全）

```python
from data.storage.parquet_reader import EnhancedParquetReader

# 创建增强型读取器
enhanced_reader = EnhancedParquetReader(
    enable_type_checking=True,        # 启用类型检查
    enable_performance_monitoring=True # 启用性能监控
)

# 类型安全的数据读取
df = enhanced_reader.read_partitioned_data(
    data_root="D:/data",
    symbol="000001.SZ",
    period="1d",
    start_time="20240101",
    end_time="20241231",
    data_type="raw"  # 或 "adjusted" 用于复权数据
)

# 获取读取统计信息
stats = enhanced_reader.get_statistics()
print(f"读取成功率: {stats['success_rate']:.2%}")
print(f"类型修复次数: {stats['type_fixes']}")
print(f"总读取次数: {stats['total_reads']}")

# 安全合并多个DataFrame
dfs = [df1, df2, df3]  # 多个DataFrame
merged_df = enhanced_reader._safe_merge_dataframes(dfs, context=None)
```

### 性能分析

使用存储性能分析工具找到最佳的线程数设置：

```python
from tools.storage_performance_analyzer import analyze_storage_performance

# 分析性能
results = analyze_storage_performance(
    df,
    data_root="./test_data",
    symbol="000001.SZ",
    period="1d",
    thread_counts=[1, 2, 4, 8],
    use_threading=True
)
```

详细使用方法请参考 [存储性能分析工具文档](../docs/storage_performance_analyzer.md)。

## 配置项

在 `config/settings.py` 中可以设置以下配置项：

```python
# 是否启用并行存储
ENABLE_PARALLEL_STORAGE = True

# 存储使用的最大线程/进程数
STORAGE_MAX_WORKERS = 4

# 是否使用多线程（False则使用多进程）
STORAGE_USE_THREADING = True
```

## 注意事项

1. 对于大型数据集，建议使用并行处理功能
2. 压缩算法会影响存储空间和读写速度，可以根据需要选择
3. 增量更新时会自动处理重叠数据，默认保留已有数据
4. 分区存储支持按日期或年份分区，根据数据周期自动选择合适的分区策略
5. **所有数据操作必须保持索引格式一致性，使用IndexManager进行安全合并**

## 索引格式最佳实践

⚠️ **重要提醒**：为确保数据索引格式一致性，请遵循以下最佳实践：

### 数据合并操作
```python
# ✅ 正确做法：使用IndexManager.safe_concat()
from utils.data_processor.index_manager import IndexManager
merged_df = IndexManager.safe_concat([df1, df2])

# ❌ 错误做法：使用ignore_index=True
merged_df = pd.concat([df1, df2], ignore_index=True)  # 会破坏索引格式！
```

### 索引格式验证
```python
# 验证索引格式
is_valid = IndexManager.validate_index_format(df)
if not is_valid:
    # 修复索引格式
    df = IndexManager.ensure_proper_index(df, time_column='time')
```

### 数据读取验证
```python
# 向量化读取（自动保持索引格式）
from data.storage.vectorized_reader import read_partitioned_data_vectorized
df = read_partitioned_data_vectorized(data_root, symbol, period)

# 验证读取结果的索引格式
IndexManager.log_index_info(df, "数据读取后")
```

### 故障排除

如果发现数据索引变成数字序列(0,1,2,3...)，说明使用了错误的合并方式：

1. **检查代码**：确保没有使用`ignore_index=True`
2. **使用IndexManager**：所有合并操作都使用`IndexManager.safe_concat()`
3. **验证修复**：使用`IndexManager.ensure_proper_index()`修复错误索引
4. **测试验证**：运行索引格式测试确保修复成功

## 更新日志

### 2025-07-31
- **重要修复**: 修复了optimize_numeric_table_creation函数中索引丢失的bug
  - 问题：函数硬编码preserve_index=False，导致保存的数据丢失索引信息
  - 修复：正确使用传入的preserve_index参数，确保索引数据正确保留
  - 影响：解决了显示数据有索引但保存数据无索引的问题
  - 测试：通过完整的索引保留验证测试
- 优化了数据存储性能
- 改进了错误处理机制
- 增强了日志记录功能